import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Modal,
  Alert,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
// Temporarily disable Jazz imports for React Native compatibility
// import { useComponent, useComponentCollection } from '@/lib/jazz/hooks';
// import { useJazzAuth } from '@/lib/jazz/provider';
// import CollaborationOverlay from './CollaborationOverlay';
import LoadingSpinner from './LoadingSpinner';
import * as Haptics from 'expo-haptics';

interface ComponentGalleryProps {
  collectionId?: string;
  editable?: boolean;
  showCollaboration?: boolean;
}

interface ComponentCardProps {
  component: any;
  onEdit?: (component: any) => void;
  onDelete?: (componentId: string) => void;
  onLike?: (componentId: string) => void;
  isEditable?: boolean;
}

function ComponentCard({ 
  component, 
  onEdit, 
  onDelete, 
  onLike, 
  isEditable = false 
}: ComponentCardProps) {
  const handlePress = () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
  };

  const handleLike = () => {
    handlePress();
    onLike?.(component.id);
  };

  const handleEdit = () => {
    handlePress();
    onEdit?.(component);
  };

  const handleDelete = () => {
    handlePress();
    Alert.alert(
      'Delete Component',
      `Are you sure you want to delete "${component.name}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Delete', 
          style: 'destructive',
          onPress: () => onDelete?.(component.id)
        },
      ]
    );
  };

  return (
    <View style={styles.componentCard}>
      {/* Component Header */}
      <View style={styles.componentHeader}>
        <View style={styles.componentInfo}>
          <Text style={styles.componentName}>{component.name}</Text>
          <Text style={styles.componentCategory}>{component.category}</Text>
        </View>
        
        <View style={styles.componentActions}>
          <TouchableOpacity style={styles.actionButton} onPress={handleLike}>
            <Ionicons name="heart-outline" size={16} color="#FF6B6B" />
            <Text style={styles.actionText}>{component.likes || 0}</Text>
          </TouchableOpacity>
          
          {isEditable && (
            <>
              <TouchableOpacity style={styles.actionButton} onPress={handleEdit}>
                <Ionicons name="pencil" size={16} color="#0099FF" />
              </TouchableOpacity>
              
              <TouchableOpacity style={styles.actionButton} onPress={handleDelete}>
                <Ionicons name="trash-outline" size={16} color="#FF6B6B" />
              </TouchableOpacity>
            </>
          )}
        </View>
      </View>

      {/* Component Description */}
      <Text style={styles.componentDescription} numberOfLines={2}>
        {component.description}
      </Text>

      {/* Component Tags */}
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        style={styles.tagsContainer}
      >
        {component.tags?.map((tag: string, index: number) => (
          <View key={index} style={styles.tag}>
            <Text style={styles.tagText}>{tag}</Text>
          </View>
        ))}
      </ScrollView>

      {/* Component Code Preview */}
      <View style={styles.codePreview}>
        <Text style={styles.codeText} numberOfLines={3}>
          {component.code}
        </Text>
      </View>

      {/* Component Meta */}
      <View style={styles.componentMeta}>
        <Text style={styles.metaText}>
          by {component.author} • {component.version || '1.0.0'}
        </Text>
        <Text style={styles.metaText}>
          {component.downloads || 0} downloads
        </Text>
      </View>
    </View>
  );
}

export default function ComponentGallery({
  collectionId,
  editable = false,
  showCollaboration = true
}: ComponentGalleryProps) {
  // Temporarily disable Jazz while fixing React Native compatibility
  const isAuthenticated = false;
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [components, setComponents] = useState([
    {
      id: '1',
      name: 'Button Component',
      description: 'A reusable button component with multiple variants',
      category: 'UI',
      tags: ['button', 'ui', 'interactive'],
      code: `<TouchableOpacity style={styles.button}>
  <Text style={styles.buttonText}>Click me</Text>
</TouchableOpacity>`,
      author: 'Demo User',
      likes: 5,
      downloads: 12,
      createdAt: new Date().toISOString(),
    },
    {
      id: '2',
      name: 'Card Component',
      description: 'A flexible card component for displaying content',
      category: 'Layout',
      tags: ['card', 'layout', 'container'],
      code: `<View style={styles.card}>
  <Text style={styles.title}>Card Title</Text>
  <Text style={styles.content}>Card content goes here</Text>
</View>`,
      author: 'Demo User',
      likes: 8,
      downloads: 20,
      createdAt: new Date().toISOString(),
    },
    {
      id: '3',
      name: 'Input Field',
      description: 'Customizable input field with validation',
      category: 'Forms',
      tags: ['input', 'form', 'validation'],
      code: `<TextInput
  style={styles.input}
  placeholder="Enter text..."
  value={value}
  onChangeText={setValue}
/>`,
      author: 'Demo User',
      likes: 12,
      downloads: 35,
      createdAt: new Date().toISOString(),
    }
  ]);

  const categories = ['all', 'UI', 'Layout', 'Forms', 'Navigation'];

  const filteredComponents = components.filter(component => {
    const matchesSearch = component.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         component.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         component.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    const matchesCategory = selectedCategory === 'all' || component.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const handleLike = (componentId: string) => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    setComponents(prev => prev.map(comp =>
      comp.id === componentId
        ? { ...comp, likes: comp.likes + 1 }
        : comp
    ));
  };

  const handleDelete = (componentId: string) => {
    Alert.alert(
      'Delete Component',
      'Are you sure you want to delete this component?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            setComponents(prev => prev.filter(comp => comp.id !== componentId));
          }
        }
      ]
    );
  };
  const [editingComponent, setEditingComponent] = useState<any>(null);
  const [newComponent, setNewComponent] = useState({
    name: '',
    description: '',
    category: 'UI',
    code: '',
    tags: '',
  });

  const handleCreateComponent = async () => {
    if (!newComponent.name.trim() || !newComponent.code.trim()) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    try {
      const newComp = {
        id: Date.now().toString(),
        name: newComponent.name.trim(),
        description: newComponent.description.trim(),
        category: newComponent.category,
        code: newComponent.code.trim(),
        tags: newComponent.tags.split(',').map(tag => tag.trim()).filter(Boolean),
        author: 'You',
        likes: 0,
        downloads: 0,
        createdAt: new Date().toISOString(),
      };

      setComponents(prev => [newComp, ...prev]);

      setNewComponent({
        name: '',
        description: '',
        category: 'UI',
        code: '',
        tags: '',
      });
      setShowCreateModal(false);

      Alert.alert('Success', 'Component created successfully!');
    } catch (error) {
      console.error('Failed to create component:', error);
      Alert.alert('Error', 'Failed to create component. Please try again.');
    }
  };

  const handleEditComponent = (component: any) => {
    setEditingComponent(component);
    setNewComponent({
      name: component.name,
      description: component.description,
      category: component.category,
      code: component.code,
      tags: component.tags?.join(', ') || '',
    });
    setShowCreateModal(true);
  };

  const handleUpdateComponent = async () => {
    if (!editingComponent || !newComponent.name.trim() || !newComponent.code.trim()) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    try {
      setComponents(prev => prev.map(comp =>
        comp.id === editingComponent.id
          ? {
              ...comp,
              name: newComponent.name.trim(),
              description: newComponent.description.trim(),
              category: newComponent.category,
              code: newComponent.code.trim(),
              tags: newComponent.tags.split(',').map(tag => tag.trim()).filter(Boolean),
            }
          : comp
      ));

      setEditingComponent(null);
      setNewComponent({
        name: '',
        description: '',
        category: 'UI',
        code: '',
        tags: '',
      });
      setShowCreateModal(false);

      Alert.alert('Success', 'Component updated successfully!');
    } catch (error) {
      console.error('Failed to update component:', error);
      Alert.alert('Error', 'Failed to update component. Please try again.');
    }
  };

  // Note: Jazz collaboration is temporarily disabled for React Native compatibility
  // Component gallery is working with local state for now

  return (
    <View style={styles.container}>
      {/* Search and Filter */}
      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <Ionicons name="search" size={20} color="#6B7280" />
          <TextInput
            style={styles.searchInput}
            placeholder="Search components..."
            placeholderTextColor="#6B7280"
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
        </View>
      </View>

      {/* Category Filter */}
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.categoryContainer}
        contentContainerStyle={styles.categoryContent}
      >
        {categories.map((category) => (
          <TouchableOpacity
            key={category}
            style={[
              styles.categoryButton,
              selectedCategory === category && styles.categoryButtonActive
            ]}
            onPress={() => setSelectedCategory(category)}
          >
            <Text style={[
              styles.categoryButtonText,
              selectedCategory === category && styles.categoryButtonTextActive
            ]}>
              {category === 'all' ? 'All' : category}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>

      {/* Header */}
      <View style={styles.header}>
        <View>
          <Text style={styles.title}>Component Gallery</Text>
          <Text style={styles.subtitle}>
            {filteredComponents.length} component{filteredComponents.length !== 1 ? 's' : ''}
          </Text>
        </View>

        {editable && (
          <TouchableOpacity
            style={styles.createButton}
            onPress={() => setShowCreateModal(true)}
          >
            <Ionicons name="add" size={20} color="#FFFFFF" />
            <Text style={styles.createButtonText}>Create</Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Components Grid */}
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {filteredComponents.map((component: any) => (
          <ComponentCard
            key={component.id}
            component={component}
            onEdit={editable ? handleEditComponent : undefined}
            onDelete={editable ? handleDelete : undefined}
            onLike={handleLike}
            isEditable={editable}
          />
        ))}

        {filteredComponents.length === 0 && (
          <View style={styles.emptyState}>
            <Ionicons name="code-slash" size={48} color="#6B7280" />
            <Text style={styles.emptyTitle}>No Components Yet</Text>
            <Text style={styles.emptyDescription}>
              {editable 
                ? "Create your first component to get started"
                : "This collection doesn't have any components yet"
              }
            </Text>
          </View>
        )}
      </ScrollView>

      {/* Collaboration features temporarily disabled for React Native compatibility */}

      {/* Create/Edit Component Modal */}
      <Modal
        visible={showCreateModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => {
          setShowCreateModal(false);
          setEditingComponent(null);
        }}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity
              onPress={() => {
                setShowCreateModal(false);
                setEditingComponent(null);
              }}
            >
              <Ionicons name="close" size={24} color="#FFFFFF" />
            </TouchableOpacity>
            
            <Text style={styles.modalTitle}>
              {editingComponent ? 'Edit Component' : 'Create Component'}
            </Text>
            
            <TouchableOpacity
              onPress={editingComponent ? handleUpdateComponent : handleCreateComponent}
            >
              <Text style={styles.saveButton}>Save</Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Name *</Text>
              <TextInput
                style={styles.input}
                value={newComponent.name}
                onChangeText={(text) => setNewComponent(prev => ({ ...prev, name: text }))}
                placeholder="Component name"
                placeholderTextColor="#6B7280"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Description</Text>
              <TextInput
                style={[styles.input, styles.textArea]}
                value={newComponent.description}
                onChangeText={(text) => setNewComponent(prev => ({ ...prev, description: text }))}
                placeholder="Describe your component"
                placeholderTextColor="#6B7280"
                multiline
                numberOfLines={3}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Category</Text>
              <TextInput
                style={styles.input}
                value={newComponent.category}
                onChangeText={(text) => setNewComponent(prev => ({ ...prev, category: text }))}
                placeholder="UI, Animation, Layout, etc."
                placeholderTextColor="#6B7280"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Tags</Text>
              <TextInput
                style={styles.input}
                value={newComponent.tags}
                onChangeText={(text) => setNewComponent(prev => ({ ...prev, tags: text }))}
                placeholder="react, button, animation (comma separated)"
                placeholderTextColor="#6B7280"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Code *</Text>
              <TextInput
                style={[styles.input, styles.codeInput]}
                value={newComponent.code}
                onChangeText={(text) => setNewComponent(prev => ({ ...prev, code: text }))}
                placeholder="Paste your component code here..."
                placeholderTextColor="#6B7280"
                multiline
                numberOfLines={10}
                textAlignVertical="top"
              />
            </View>
          </ScrollView>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  authPrompt: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
    gap: 16,
  },
  authPromptTitle: {
    color: '#FFFFFF',
    fontSize: 20,
    fontWeight: '600',
  },
  authPromptText: {
    color: '#9CA3AF',
    fontSize: 14,
    textAlign: 'center',
    maxWidth: 280,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    padding: 16,
    paddingTop: 60,
    gap: 16,
  },
  title: {
    color: '#FFFFFF',
    fontSize: 24,
    fontWeight: '600',
  },
  subtitle: {
    color: '#9CA3AF',
    fontSize: 14,
    marginTop: 4,
  },
  createButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    backgroundColor: '#0099FF',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 12,
  },
  createButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingTop: 0,
    paddingBottom: 100,
    gap: 16,
  },
  componentCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 16,
    padding: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  componentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  componentInfo: {
    flex: 1,
  },
  componentName: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  componentCategory: {
    color: '#0099FF',
    fontSize: 12,
    fontWeight: '500',
    marginTop: 2,
  },
  componentActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    padding: 6,
  },
  actionText: {
    color: '#9CA3AF',
    fontSize: 12,
  },
  componentDescription: {
    color: '#D1D5DB',
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 12,
  },
  tagsContainer: {
    marginBottom: 12,
  },
  tag: {
    backgroundColor: 'rgba(0, 153, 255, 0.1)',
    borderColor: 'rgba(0, 153, 255, 0.3)',
    borderWidth: 1,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
    marginRight: 6,
  },
  tagText: {
    color: '#0099FF',
    fontSize: 10,
    fontWeight: '500',
  },
  codePreview: {
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
  },
  codeText: {
    color: '#E5E7EB',
    fontSize: 12,
    fontFamily: Platform.OS === 'ios' ? 'Menlo' : 'monospace',
    lineHeight: 16,
  },
  componentMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  metaText: {
    color: '#6B7280',
    fontSize: 11,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 48,
    gap: 16,
  },
  emptyTitle: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
  },
  emptyDescription: {
    color: '#9CA3AF',
    fontSize: 14,
    textAlign: 'center',
    maxWidth: 280,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#000000',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    paddingTop: 60,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  modalTitle: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
  },
  saveButton: {
    color: '#0099FF',
    fontSize: 16,
    fontWeight: '600',
  },
  modalContent: {
    flex: 1,
    padding: 16,
  },
  inputGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  input: {
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: 16,
    color: '#FFFFFF',
    fontSize: 16,
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  codeInput: {
    height: 200,
    textAlignVertical: 'top',
    fontFamily: Platform.OS === 'ios' ? 'Menlo' : 'monospace',
    fontSize: 14,
  },
  searchContainer: {
    padding: 16,
    paddingBottom: 8,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 12,
  },
  searchInput: {
    flex: 1,
    color: '#FFFFFF',
    fontSize: 16,
  },
  categoryContainer: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  categoryContent: {
    gap: 8,
  },
  categoryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  categoryButtonActive: {
    backgroundColor: '#0099FF',
    borderColor: '#0099FF',
  },
  categoryButtonText: {
    color: '#9CA3AF',
    fontSize: 14,
    fontWeight: '500',
  },
  categoryButtonTextActive: {
    color: '#FFFFFF',
  },
});
