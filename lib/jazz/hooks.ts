import { useCoState, useAccount } from 'jazz-tools/react';
import { 
  Component, 
  ComponentCollection, 
  CommentsThread, 
  Comment,
  UserPresence,
  CollaborationSession,
  Workspace,
  ActivityFeed,
  NotificationList,
  JazzAccount 
} from './schemas';
import { generateId, getUserPresenceColor } from './provider';

// Hook for managing components
export function useComponent(componentId?: string) {
  const account = useAccount<JazzAccount>();
  const component = useCoState(Component, componentId);
  
  const createComponent = (data: {
    name: string;
    description: string;
    category: string;
    code: string;
    tags?: string[];
    isPublic?: boolean;
  }) => {
    if (!account) throw new Error('No account available');
    
    return Component.create({
      name: data.name,
      description: data.description,
      category: data.category,
      code: data.code,
      tags: data.tags || [],
      author: account.id,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      likes: 0,
      downloads: 0,
      isPublic: data.isPublic ?? true,
      version: '1.0.0',
    }, account);
  };
  
  const updateComponent = (updates: Partial<{
    name: string;
    description: string;
    code: string;
    tags: string[];
    isPublic: boolean;
  }>) => {
    if (!component) throw new Error('No component available');
    
    Object.assign(component, {
      ...updates,
      updatedAt: new Date().toISOString(),
    });
    
    return component;
  };
  
  const likeComponent = () => {
    if (!component) throw new Error('No component available');
    
    component.likes = (component.likes || 0) + 1;
    return component;
  };
  
  return {
    component,
    createComponent,
    updateComponent,
    likeComponent,
    isLoading: componentId && !component,
  };
}

// Hook for managing component collections
export function useComponentCollection(collectionId?: string) {
  const account = useAccount<JazzAccount>();
  const collection = useCoState(ComponentCollection, collectionId);
  
  const createCollection = (data: {
    name: string;
    description: string;
    isPublic?: boolean;
    tags?: string[];
  }) => {
    if (!account) throw new Error('No account available');
    
    return ComponentCollection.create({
      name: data.name,
      description: data.description,
      components: [],
      owner: account.id,
      collaborators: [],
      isPublic: data.isPublic ?? true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      tags: data.tags || [],
    }, account);
  };
  
  const addComponent = (component: Component) => {
    if (!collection) throw new Error('No collection available');
    
    collection.components.push(component);
    collection.updatedAt = new Date().toISOString();
    
    return collection;
  };
  
  const removeComponent = (componentId: string) => {
    if (!collection) throw new Error('No collection available');
    
    const index = collection.components.findIndex(c => c.id === componentId);
    if (index !== -1) {
      collection.components.splice(index, 1);
      collection.updatedAt = new Date().toISOString();
    }
    
    return collection;
  };
  
  const addCollaborator = (userId: string) => {
    if (!collection) throw new Error('No collection available');
    
    if (!collection.collaborators.includes(userId)) {
      collection.collaborators.push(userId);
      collection.updatedAt = new Date().toISOString();
    }
    
    return collection;
  };
  
  return {
    collection,
    createCollection,
    addComponent,
    removeComponent,
    addCollaborator,
    isLoading: collectionId && !collection,
  };
}

// Hook for managing comments
export function useComments(componentId: string) {
  const account = useAccount<JazzAccount>();
  const commentsThread = useCoState(CommentsThread, `comments-${componentId}`);
  
  const createCommentsThread = () => {
    if (!account) throw new Error('No account available');
    
    return CommentsThread.create({
      componentId,
      comments: [],
      createdAt: new Date().toISOString(),
    }, account);
  };
  
  const addComment = (content: string, parentId?: string) => {
    if (!account) throw new Error('No account available');
    
    let thread = commentsThread;
    if (!thread) {
      thread = createCommentsThread();
    }
    
    const comment = Comment.create({
      content,
      author: account.id,
      authorName: account.profile?.name || 'Anonymous',
      createdAt: new Date().toISOString(),
      parentId,
      likes: 0,
      isEdited: false,
    }, account);
    
    thread.comments.push(comment);
    
    return comment;
  };
  
  const updateComment = (commentId: string, content: string) => {
    if (!commentsThread) throw new Error('No comments thread available');
    
    const comment = commentsThread.comments.find(c => c.id === commentId);
    if (!comment) throw new Error('Comment not found');
    
    comment.content = content;
    comment.updatedAt = new Date().toISOString();
    comment.isEdited = true;
    
    return comment;
  };
  
  const likeComment = (commentId: string) => {
    if (!commentsThread) throw new Error('No comments thread available');
    
    const comment = commentsThread.comments.find(c => c.id === commentId);
    if (!comment) throw new Error('Comment not found');
    
    comment.likes = (comment.likes || 0) + 1;
    
    return comment;
  };
  
  return {
    comments: commentsThread?.comments || [],
    addComment,
    updateComment,
    likeComment,
    isLoading: !commentsThread,
  };
}

// Hook for user presence and collaboration
export function usePresence(componentId?: string) {
  const account = useAccount<JazzAccount>();
  const sessionId = componentId ? `session-${componentId}` : undefined;
  const session = useCoState(CollaborationSession, sessionId);
  
  const joinSession = (componentId: string) => {
    if (!account) throw new Error('No account available');
    
    let collaborationSession = session;
    if (!collaborationSession) {
      collaborationSession = CollaborationSession.create({
        componentId,
        participants: [],
        createdAt: new Date().toISOString(),
        isActive: true,
      }, account);
    }
    
    // Add or update user presence
    const existingPresence = collaborationSession.participants.find(p => p.userId === account.id);
    
    if (existingPresence) {
      existingPresence.isActive = true;
      existingPresence.lastSeen = new Date().toISOString();
    } else {
      const presence = UserPresence.create({
        userId: account.id,
        userName: account.profile?.name || 'Anonymous',
        avatar: account.profile?.avatar,
        currentComponent: componentId,
        isActive: true,
        lastSeen: new Date().toISOString(),
        color: getUserPresenceColor(account.id),
      }, account);
      
      collaborationSession.participants.push(presence);
    }
    
    return collaborationSession;
  };
  
  const updateCursor = (x: number, y: number) => {
    if (!session || !account) return;
    
    const presence = session.participants.find(p => p.userId === account.id);
    if (presence) {
      presence.cursor = { x, y };
      presence.lastSeen = new Date().toISOString();
    }
  };
  
  const leaveSession = () => {
    if (!session || !account) return;
    
    const presence = session.participants.find(p => p.userId === account.id);
    if (presence) {
      presence.isActive = false;
      presence.lastSeen = new Date().toISOString();
    }
  };
  
  return {
    session,
    participants: session?.participants || [],
    joinSession,
    updateCursor,
    leaveSession,
    isLoading: sessionId && !session,
  };
}

// Hook for workspace management
export function useWorkspace(workspaceId?: string) {
  const account = useAccount<JazzAccount>();
  const workspace = useCoState(Workspace, workspaceId);
  
  const createWorkspace = (data: {
    name: string;
    description: string;
    isPublic?: boolean;
  }) => {
    if (!account) throw new Error('No account available');
    
    return Workspace.create({
      name: data.name,
      description: data.description,
      owner: account.id,
      members: [account.id],
      collections: [],
      settings: {
        isPublic: data.isPublic ?? false,
        allowComments: true,
        allowForks: true,
      },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }, account);
  };
  
  const addMember = (userId: string) => {
    if (!workspace) throw new Error('No workspace available');
    
    if (!workspace.members.includes(userId)) {
      workspace.members.push(userId);
      workspace.updatedAt = new Date().toISOString();
    }
    
    return workspace;
  };
  
  const addCollection = (collection: ComponentCollection) => {
    if (!workspace) throw new Error('No workspace available');
    
    workspace.collections.push(collection);
    workspace.updatedAt = new Date().toISOString();
    
    return workspace;
  };
  
  return {
    workspace,
    createWorkspace,
    addMember,
    addCollection,
    isLoading: workspaceId && !workspace,
  };
}
