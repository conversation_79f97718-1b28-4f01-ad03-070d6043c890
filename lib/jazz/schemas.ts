import { co, z } from "jazz-tools";

// User Profile for Jazz
export const JazzUserProfile = co.map({
  name: z.string(),
  avatar: z.string().optional(),
  bio: z.string().optional(),
  preferences: co.map({
    theme: z.enum(["light", "dark", "auto"]).optional(),
    notifications: z.boolean().optional(),
  }).optional(),
});

// Component in the gallery
export const Component = co.map({
  name: z.string(),
  description: z.string(),
  category: z.string(),
  tags: co.list(z.string()),
  code: z.string(),
  preview: z.string().optional(), // Base64 image or URL
  author: z.string(), // User ID
  createdAt: z.string(),
  updatedAt: z.string(),
  likes: z.number().optional(),
  downloads: z.number().optional(),
  isPublic: z.boolean(),
  version: z.string().optional(),
});

// Collection of components
export const ComponentCollection = co.map({
  name: z.string(),
  description: z.string(),
  components: co.list(Component),
  owner: z.string(), // User ID
  collaborators: co.list(z.string()), // User IDs
  isPublic: z.boolean(),
  createdAt: z.string(),
  updatedAt: z.string(),
  tags: co.list(z.string()),
});

// Real-time comment on a component
export const Comment = co.map({
  content: z.string(),
  author: z.string(), // User ID
  authorName: z.string(),
  createdAt: z.string(),
  updatedAt: z.string().optional(),
  parentId: z.string().optional(), // For nested comments
  likes: z.number().optional(),
  isEdited: z.boolean().optional(),
});

// Comments thread for a component
export const CommentsThread = co.map({
  componentId: z.string(),
  comments: co.list(Comment),
  createdAt: z.string(),
});

// User presence for real-time collaboration
export const UserPresence = co.map({
  userId: z.string(),
  userName: z.string(),
  avatar: z.string().optional(),
  currentPage: z.string().optional(),
  currentComponent: z.string().optional(),
  cursor: co.map({
    x: z.number(),
    y: z.number(),
  }).optional(),
  isActive: z.boolean(),
  lastSeen: z.string(),
  color: z.string(), // Unique color for this user's cursor/presence
});

// Live collaboration session
export const CollaborationSession = co.map({
  componentId: z.string(),
  participants: co.list(UserPresence),
  createdAt: z.string(),
  isActive: z.boolean(),
});

// Shared workspace for teams
export const Workspace = co.map({
  name: z.string(),
  description: z.string(),
  owner: z.string(),
  members: co.list(z.string()), // User IDs
  collections: co.list(ComponentCollection),
  settings: co.map({
    isPublic: z.boolean(),
    allowComments: z.boolean(),
    allowForks: z.boolean(),
  }),
  createdAt: z.string(),
  updatedAt: z.string(),
});

// Activity feed for workspace
export const Activity = co.map({
  type: z.enum([
    "component_created",
    "component_updated", 
    "component_liked",
    "comment_added",
    "collection_created",
    "user_joined",
    "component_forked"
  ]),
  userId: z.string(),
  userName: z.string(),
  targetId: z.string(), // Component/Collection ID
  targetName: z.string(),
  description: z.string(),
  timestamp: z.string(),
  metadata: co.map({
    oldValue: z.string().optional(),
    newValue: z.string().optional(),
  }).optional(),
});

// Activity feed list
export const ActivityFeed = co.list(Activity);

// Notification for users
export const Notification = co.map({
  type: z.enum([
    "comment_reply",
    "component_liked", 
    "collection_shared",
    "workspace_invite",
    "mention"
  ]),
  title: z.string(),
  message: z.string(),
  userId: z.string(), // Recipient
  fromUserId: z.string(), // Sender
  fromUserName: z.string(),
  targetId: z.string().optional(),
  isRead: z.boolean(),
  createdAt: z.string(),
});

// User's notification list
export const NotificationList = co.list(Notification);

// File upload for component assets
export const ComponentAsset = co.map({
  name: z.string(),
  type: z.string(), // MIME type
  size: z.number(),
  url: z.string(),
  componentId: z.string(),
  uploadedBy: z.string(),
  uploadedAt: z.string(),
});

// Jazz Account extending the base account
export const JazzAccount = co.map({
  profile: JazzUserProfile,
  workspaces: co.list(Workspace),
  notifications: NotificationList,
  preferences: co.map({
    defaultWorkspace: z.string().optional(),
    emailNotifications: z.boolean().optional(),
    pushNotifications: z.boolean().optional(),
  }).optional(),
});

// Export all schemas for easy importing
export const schemas = {
  JazzUserProfile,
  Component,
  ComponentCollection,
  Comment,
  CommentsThread,
  UserPresence,
  CollaborationSession,
  Workspace,
  Activity,
  ActivityFeed,
  Notification,
  NotificationList,
  ComponentAsset,
  JazzAccount,
};
