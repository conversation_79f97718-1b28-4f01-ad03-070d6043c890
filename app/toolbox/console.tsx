import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";

const CONSOLE_TABS = [
  { id: "profile", name: "Profile", icon: "person-outline" },
  { id: "data", name: "Data", icon: "cloud-upload-outline" },
  { id: "invoices", name: "Invoices", icon: "receipt-outline" },
  { id: "roadmap", name: "Roadmap", icon: "map-outline" },
];

export default function Console() {
  const currentUser = useQuery(api.auth.getCurrentUser);
  const clientProfile = useQuery(api.clients.getProfile);
  const invoices = useQuery(api.invoices.getUserInvoices);
  const roadmap = useQuery(api.roadmaps.getUserRoadmap);
  
  const [activeTab, setActiveTab] = useState("profile");

  // Check if user is a client
  if (!currentUser || currentUser.role !== "client") {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.accessDenied}>
          <Ionicons name="lock-closed-outline" size={48} color="#6B7280" />
          <Text style={styles.accessDeniedTitle}>Access Restricted</Text>
          <Text style={styles.accessDeniedText}>
            Client Console is only available for client accounts.
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  const renderTabContent = () => {
    switch (activeTab) {
      case "profile":
        return (
          <View style={styles.tabContent}>
            <Text style={styles.tabTitle}>Intelligence Summary</Text>
            <View style={styles.profileCard}>
              <Text style={styles.intelligenceText}>
                {clientProfile?.intelligence || 
                "Your AI intelligence profile is being generated based on your interactions and data. This summary will help us provide more personalized assistance and recommendations."}
              </Text>
            </View>
            
            <Text style={styles.sectionTitle}>Collected Documents</Text>
            <View style={styles.documentsGrid}>
              {clientProfile?.collectedDocs?.map((doc, index) => (
                <View key={index} style={styles.documentCard}>
                  <Ionicons name="document-text-outline" size={24} color="#0099FF" />
                  <Text style={styles.documentName}>{doc}</Text>
                </View>
              )) || [
                "Project Brief.pdf",
                "Requirements.docx",
                "Brand Guidelines.pdf"
              ].map((doc, index) => (
                <View key={index} style={styles.documentCard}>
                  <Ionicons name="document-text-outline" size={24} color="#0099FF" />
                  <Text style={styles.documentName}>{doc}</Text>
                </View>
              ))}
            </View>
          </View>
        );

      case "data":
        return (
          <View style={styles.tabContent}>
            <Text style={styles.tabTitle}>Data Uploads</Text>
            <View style={styles.uploadArea}>
              <Ionicons name="cloud-upload-outline" size={48} color="#6B7280" />
              <Text style={styles.uploadTitle}>Upload Your Data</Text>
              <Text style={styles.uploadDescription}>
                Drag and drop files here or click to browse. Supported formats: PDF, DOCX, TXT, CSV
              </Text>
              <TouchableOpacity style={styles.uploadButton}>
                <Text style={styles.uploadButtonText}>Choose Files</Text>
              </TouchableOpacity>
            </View>
            
            <Text style={styles.sectionTitle}>Recent Uploads</Text>
            <View style={styles.uploadsList}>
              {[
                { name: "Q4_Report.pdf", size: "2.4 MB", date: "2 days ago", status: "processed" },
                { name: "Customer_Data.csv", size: "1.8 MB", date: "1 week ago", status: "processing" },
                { name: "Meeting_Notes.docx", size: "456 KB", date: "2 weeks ago", status: "processed" },
              ].map((file, index) => (
                <View key={index} style={styles.fileItem}>
                  <View style={styles.fileIcon}>
                    <Ionicons name="document-outline" size={20} color="#0099FF" />
                  </View>
                  <View style={styles.fileInfo}>
                    <Text style={styles.fileName}>{file.name}</Text>
                    <Text style={styles.fileDetails}>{file.size} • {file.date}</Text>
                  </View>
                  <View style={[
                    styles.statusBadge,
                    { backgroundColor: file.status === "processed" ? "#00C89620" : "#FF914D20" }
                  ]}>
                    <Text style={[
                      styles.statusText,
                      { color: file.status === "processed" ? "#00C896" : "#FF914D" }
                    ]}>
                      {file.status}
                    </Text>
                  </View>
                </View>
              ))}
            </View>
          </View>
        );

      case "invoices":
        return (
          <View style={styles.tabContent}>
            <Text style={styles.tabTitle}>Invoices & Billing</Text>
            <View style={styles.invoicesList}>
              {invoices?.map((invoice, index) => (
                <View key={index} style={styles.invoiceCard}>
                  <View style={styles.invoiceHeader}>
                    <Text style={styles.invoiceNumber}>#{invoice.stripeId}</Text>
                    <View style={[
                      styles.invoiceStatus,
                      { backgroundColor: invoice.status === "paid" ? "#00C89620" : 
                                       invoice.status === "pending" ? "#FF914D20" : "#EF444420" }
                    ]}>
                      <Text style={[
                        styles.invoiceStatusText,
                        { color: invoice.status === "paid" ? "#00C896" : 
                                 invoice.status === "pending" ? "#FF914D" : "#EF4444" }
                      ]}>
                        {invoice.status.toUpperCase()}
                      </Text>
                    </View>
                  </View>
                  <Text style={styles.invoiceAmount}>
                    {invoice.currency.toUpperCase()} {invoice.amount}
                  </Text>
                  <Text style={styles.invoicePeriod}>
                    {invoice.periodStart} - {invoice.periodEnd}
                  </Text>
                  {invoice.pdf && (
                    <TouchableOpacity style={styles.downloadButton}>
                      <Ionicons name="download-outline" size={16} color="#0099FF" />
                      <Text style={styles.downloadText}>Download PDF</Text>
                    </TouchableOpacity>
                  )}
                </View>
              )) || [
                {
                  stripeId: "inv_1234567890",
                  amount: 99,
                  currency: "usd",
                  periodStart: "Jan 1, 2025",
                  periodEnd: "Jan 31, 2025",
                  status: "paid"
                },
                {
                  stripeId: "inv_0987654321",
                  amount: 99,
                  currency: "usd",
                  periodStart: "Dec 1, 2024",
                  periodEnd: "Dec 31, 2024",
                  status: "paid"
                }
              ].map((invoice, index) => (
                <View key={index} style={styles.invoiceCard}>
                  <View style={styles.invoiceHeader}>
                    <Text style={styles.invoiceNumber}>#{invoice.stripeId}</Text>
                    <View style={[
                      styles.invoiceStatus,
                      { backgroundColor: invoice.status === "paid" ? "#00C89620" : 
                                       invoice.status === "pending" ? "#FF914D20" : "#EF444420" }
                    ]}>
                      <Text style={[
                        styles.invoiceStatusText,
                        { color: invoice.status === "paid" ? "#00C896" : 
                                 invoice.status === "pending" ? "#FF914D" : "#EF4444" }
                      ]}>
                        {invoice.status.toUpperCase()}
                      </Text>
                    </View>
                  </View>
                  <Text style={styles.invoiceAmount}>
                    {invoice.currency.toUpperCase()} ${invoice.amount}
                  </Text>
                  <Text style={styles.invoicePeriod}>
                    {invoice.periodStart} - {invoice.periodEnd}
                  </Text>
                  <TouchableOpacity style={styles.downloadButton}>
                    <Ionicons name="download-outline" size={16} color="#0099FF" />
                    <Text style={styles.downloadText}>Download PDF</Text>
                  </TouchableOpacity>
                </View>
              ))}
            </View>
          </View>
        );

      case "roadmap":
        return (
          <View style={styles.tabContent}>
            <Text style={styles.tabTitle}>Project Roadmap</Text>
            <View style={styles.roadmapList}>
              {roadmap?.items?.map((item, index) => (
                <View key={index} style={styles.roadmapItem}>
                  <View style={[
                    styles.roadmapStatus,
                    { backgroundColor: item.status === "completed" ? "#00C896" : 
                                     item.status === "in_progress" ? "#0099FF" : "#6B7280" }
                  ]} />
                  <View style={styles.roadmapContent}>
                    <Text style={styles.roadmapTitle}>{item.title}</Text>
                    {item.description && (
                      <Text style={styles.roadmapDescription}>{item.description}</Text>
                    )}
                    {item.due && (
                      <Text style={styles.roadmapDue}>Due: {item.due}</Text>
                    )}
                  </View>
                </View>
              )) || [
                {
                  title: "Project Discovery & Planning",
                  description: "Initial consultation and requirement gathering",
                  status: "completed",
                  due: "Completed"
                },
                {
                  title: "Design & Prototyping",
                  description: "UI/UX design and interactive prototypes",
                  status: "in_progress",
                  due: "Feb 15, 2025"
                },
                {
                  title: "Development Phase 1",
                  description: "Core functionality implementation",
                  status: "planned",
                  due: "Mar 30, 2025"
                },
                {
                  title: "Testing & QA",
                  description: "Comprehensive testing and quality assurance",
                  status: "planned",
                  due: "Apr 15, 2025"
                },
                {
                  title: "Launch & Deployment",
                  description: "Production deployment and go-live",
                  status: "planned",
                  due: "May 1, 2025"
                }
              ].map((item, index) => (
                <View key={index} style={styles.roadmapItem}>
                  <View style={[
                    styles.roadmapStatus,
                    { backgroundColor: item.status === "completed" ? "#00C896" : 
                                     item.status === "in_progress" ? "#0099FF" : "#6B7280" }
                  ]} />
                  <View style={styles.roadmapContent}>
                    <Text style={styles.roadmapTitle}>{item.title}</Text>
                    {item.description && (
                      <Text style={styles.roadmapDescription}>{item.description}</Text>
                    )}
                    <Text style={styles.roadmapDue}>Due: {item.due}</Text>
                  </View>
                </View>
              ))}
            </View>
          </View>
        );

      default:
        return null;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons name="arrow-back" size={24} color="white" />
        </TouchableOpacity>
        <Text style={styles.title}>Client Console</Text>
      </View>

      {/* Tab Navigation */}
      <View style={styles.tabNavigation}>
        <ScrollView 
          horizontal 
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.tabsContainer}
        >
          {CONSOLE_TABS.map((tab) => (
            <TouchableOpacity
              key={tab.id}
              style={[styles.tab, activeTab === tab.id && styles.tabActive]}
              onPress={() => setActiveTab(tab.id)}
            >
              <Ionicons 
                name={tab.icon as any} 
                size={16} 
                color={activeTab === tab.id ? "white" : "#9CA3AF"} 
              />
              <Text style={[
                styles.tabText,
                activeTab === tab.id && styles.tabTextActive
              ]}>
                {tab.name}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Tab Content */}
      <ScrollView 
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {renderTabContent()}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000000",
  },
  accessDenied: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 32,
    gap: 16,
  },
  accessDeniedTitle: {
    color: "#FFFFFF",
    fontSize: 20,
    fontWeight: "600",
  },
  accessDeniedText: {
    color: "#9CA3AF",
    fontSize: 14,
    textAlign: "center",
    lineHeight: 20,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    paddingTop: 60,
    gap: 16,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#18181B",
    justifyContent: "center",
    alignItems: "center",
  },
  title: {
    color: "#FFFFFF",
    fontSize: 24,
    fontWeight: "600",
    letterSpacing: -0.5,
  },
  tabNavigation: {
    borderBottomWidth: 1,
    borderBottomColor: "#18181B",
  },
  tabsContainer: {
    paddingHorizontal: 16,
    gap: 8,
  },
  tab: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 20,
    gap: 6,
  },
  tabActive: {
    backgroundColor: "#0099FF",
  },
  tabText: {
    color: "#9CA3AF",
    fontSize: 14,
    fontWeight: "500",
  },
  tabTextActive: {
    color: "white",
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 100,
  },
  tabContent: {
    gap: 24,
  },
  tabTitle: {
    color: "#FFFFFF",
    fontSize: 20,
    fontWeight: "600",
    letterSpacing: -0.5,
  },
  sectionTitle: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
    marginTop: 8,
  },
  profileCard: {
    backgroundColor: "#18181B",
    borderRadius: 16,
    padding: 20,
  },
  intelligenceText: {
    color: "#D1D5DB",
    fontSize: 14,
    lineHeight: 20,
  },
  documentsGrid: {
    gap: 12,
  },
  documentCard: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
    backgroundColor: "#18181B",
    padding: 16,
    borderRadius: 12,
  },
  documentName: {
    color: "#FFFFFF",
    fontSize: 14,
    fontWeight: "500",
  },
  uploadArea: {
    backgroundColor: "#18181B",
    borderRadius: 16,
    padding: 32,
    alignItems: "center",
    gap: 16,
    borderWidth: 2,
    borderColor: "#374151",
    borderStyle: "dashed",
  },
  uploadTitle: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
  },
  uploadDescription: {
    color: "#9CA3AF",
    fontSize: 14,
    textAlign: "center",
    maxWidth: 280,
  },
  uploadButton: {
    backgroundColor: "#0099FF",
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 12,
  },
  uploadButtonText: {
    color: "white",
    fontSize: 14,
    fontWeight: "500",
  },
  uploadsList: {
    gap: 12,
  },
  fileItem: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
    backgroundColor: "#18181B",
    padding: 16,
    borderRadius: 12,
  },
  fileIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#0099FF20",
    justifyContent: "center",
    alignItems: "center",
  },
  fileInfo: {
    flex: 1,
    gap: 4,
  },
  fileName: {
    color: "#FFFFFF",
    fontSize: 14,
    fontWeight: "500",
  },
  fileDetails: {
    color: "#9CA3AF",
    fontSize: 12,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  statusText: {
    fontSize: 10,
    fontWeight: "600",
    textTransform: "uppercase",
  },
  invoicesList: {
    gap: 16,
  },
  invoiceCard: {
    backgroundColor: "#18181B",
    borderRadius: 16,
    padding: 20,
    gap: 12,
  },
  invoiceHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  invoiceNumber: {
    color: "#9CA3AF",
    fontSize: 12,
    fontFamily: "monospace",
  },
  invoiceStatus: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  invoiceStatusText: {
    fontSize: 10,
    fontWeight: "600",
  },
  invoiceAmount: {
    color: "#FFFFFF",
    fontSize: 24,
    fontWeight: "700",
  },
  invoicePeriod: {
    color: "#9CA3AF",
    fontSize: 14,
  },
  downloadButton: {
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
    alignSelf: "flex-start",
    marginTop: 8,
  },
  downloadText: {
    color: "#0099FF",
    fontSize: 14,
    fontWeight: "500",
  },
  roadmapList: {
    gap: 16,
  },
  roadmapItem: {
    flexDirection: "row",
    gap: 16,
    backgroundColor: "#18181B",
    borderRadius: 16,
    padding: 20,
  },
  roadmapStatus: {
    width: 4,
    borderRadius: 2,
    marginTop: 4,
  },
  roadmapContent: {
    flex: 1,
    gap: 8,
  },
  roadmapTitle: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
  },
  roadmapDescription: {
    color: "#D1D5DB",
    fontSize: 14,
    lineHeight: 20,
  },
  roadmapDue: {
    color: "#9CA3AF",
    fontSize: 12,
  },
});