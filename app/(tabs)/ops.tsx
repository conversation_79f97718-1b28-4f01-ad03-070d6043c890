import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Ionicons } from "@expo/vector-icons";

const ISSUE_STATUS = {
  todo: { label: "To Do", color: "#6B7280" },
  in_progress: { label: "In Progress", color: "#0099FF" },
  review: { label: "Review", color: "#FF914D" },
  done: { label: "Done", color: "#00C896" },
};

const PRIORITY_COLORS = {
  low: "#6B7280",
  medium: "#0099FF",
  high: "#FF914D",
  urgent: "#EF4444",
};

export default function Ops() {
  const currentUser = useQuery(api.auth.getCurrentUser);
  const issues = useQuery(api.issues.list);
  const clientHealth = useQuery(api.ops.getClientHealth);
  const [selectedTab, setSelectedTab] = useState<"kanban" | "clients">("kanban");

  // Check permissions
  if (!currentUser || !["admin", "lead", "consultant"].includes(currentUser.role)) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.accessDenied}>
          <Ionicons name="lock-closed-outline" size={48} color="#6B7280" />
          <Text style={styles.accessDeniedTitle}>Access Restricted</Text>
          <Text style={styles.accessDeniedText}>
            Operations dashboard is not available for your role.
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  if (issues === undefined) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading...</Text>
        </View>
      </SafeAreaView>
    );
  }

  const groupedIssues = issues.reduce((acc, issue) => {
    if (!acc[issue.status]) acc[issue.status] = [];
    acc[issue.status].push(issue);
    return acc;
  }, {} as Record<string, typeof issues>);

  const canSeeClientHealth = ["admin", "lead"].includes(currentUser.role);

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Operations</Text>
        
        {canSeeClientHealth && (
          <View style={styles.tabSelector}>
            <TouchableOpacity
              style={[styles.tab, selectedTab === "kanban" && styles.tabActive]}
              onPress={() => setSelectedTab("kanban")}
            >
              <Text style={[styles.tabText, selectedTab === "kanban" && styles.tabTextActive]}>
                Kanban
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.tab, selectedTab === "clients" && styles.tabActive]}
              onPress={() => setSelectedTab("clients")}
            >
              <Text style={[styles.tabText, selectedTab === "clients" && styles.tabTextActive]}>
                Clients
              </Text>
            </TouchableOpacity>
          </View>
        )}
      </View>

      {selectedTab === "kanban" ? (
        <ScrollView 
          horizontal 
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.kanbanContainer}
        >
          {Object.entries(ISSUE_STATUS).map(([status, config]) => (
            <View key={status} style={styles.kanbanColumn}>
              <View style={styles.columnHeader}>
                <View style={[styles.statusDot, { backgroundColor: config.color }]} />
                <Text style={styles.columnTitle}>{config.label}</Text>
                <Text style={styles.columnCount}>
                  {groupedIssues[status]?.length || 0}
                </Text>
              </View>
              
              <ScrollView 
                style={styles.columnScroll}
                showsVerticalScrollIndicator={false}
              >
                {groupedIssues[status]?.map((issue) => (
                  <TouchableOpacity key={issue._id} style={styles.issueCard}>
                    <View style={styles.issueHeader}>
                      <Text style={styles.issueTitle} numberOfLines={2}>
                        {issue.title}
                      </Text>
                      <View style={[
                        styles.priorityBadge,
                        { backgroundColor: PRIORITY_COLORS[issue.priority] + "20" }
                      ]}>
                        <Text style={[
                          styles.priorityText,
                          { color: PRIORITY_COLORS[issue.priority] }
                        ]}>
                          {issue.priority.toUpperCase()}
                        </Text>
                      </View>
                    </View>
                    
                    {issue.description && (
                      <Text style={styles.issueDescription} numberOfLines={3}>
                        {issue.description}
                      </Text>
                    )}
                    
                    {issue.assignee && (
                      <View style={styles.assigneeContainer}>
                        <View style={styles.assigneeAvatar}>
                          <Text style={styles.assigneeText}>A</Text>
                        </View>
                        <Text style={styles.assigneeName}>Assigned</Text>
                      </View>
                    )}
                  </TouchableOpacity>
                )) || []}
              </ScrollView>
            </View>
          ))}
        </ScrollView>
      ) : (
        <ScrollView 
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Client Health Table */}
          <View style={styles.clientHealthSection}>
            <Text style={styles.sectionTitle}>Client Health</Text>
            
            {clientHealth?.map((client, index) => (
              <View key={index} style={styles.clientCard}>
                <View style={styles.clientHeader}>
                  <View style={styles.clientInfo}>
                    <Text style={styles.clientName}>{client.name}</Text>
                    <Text style={styles.clientProject}>{client.project}</Text>
                  </View>
                  <View style={[
                    styles.healthBadge,
                    { backgroundColor: client.health === "good" ? "#00C89620" : 
                                     client.health === "warning" ? "#FF914D20" : "#EF444420" }
                  ]}>
                    <Text style={[
                      styles.healthText,
                      { color: client.health === "good" ? "#00C896" : 
                               client.health === "warning" ? "#FF914D" : "#EF4444" }
                    ]}>
                      {client.health.toUpperCase()}
                    </Text>
                  </View>
                </View>
                
                <View style={styles.clientMetrics}>
                  <View style={styles.metric}>
                    <Text style={styles.metricLabel}>Last Contact</Text>
                    <Text style={styles.metricValue}>{client.lastContact}</Text>
                  </View>
                  <View style={styles.metric}>
                    <Text style={styles.metricLabel}>Response Time</Text>
                    <Text style={styles.metricValue}>{client.responseTime}</Text>
                  </View>
                  <View style={styles.metric}>
                    <Text style={styles.metricLabel}>Satisfaction</Text>
                    <Text style={styles.metricValue}>{client.satisfaction}/5</Text>
                  </View>
                </View>
              </View>
            )) || [
              // Demo data
              {
                name: "ARA Group",
                project: "AskARA Facility Agent",
                health: "good",
                lastContact: "2 days ago",
                responseTime: "< 2h",
                satisfaction: "4.8"
              },
              {
                name: "SANCTUM Club",
                project: "Member App",
                health: "warning",
                lastContact: "1 week ago",
                responseTime: "6h",
                satisfaction: "4.2"
              }
            ].map((client, index) => (
              <View key={index} style={styles.clientCard}>
                <View style={styles.clientHeader}>
                  <View style={styles.clientInfo}>
                    <Text style={styles.clientName}>{client.name}</Text>
                    <Text style={styles.clientProject}>{client.project}</Text>
                  </View>
                  <View style={[
                    styles.healthBadge,
                    { backgroundColor: client.health === "good" ? "#00C89620" : 
                                     client.health === "warning" ? "#FF914D20" : "#EF444420" }
                  ]}>
                    <Text style={[
                      styles.healthText,
                      { color: client.health === "good" ? "#00C896" : 
                               client.health === "warning" ? "#FF914D" : "#EF4444" }
                    ]}>
                      {client.health.toUpperCase()}
                    </Text>
                  </View>
                </View>
                
                <View style={styles.clientMetrics}>
                  <View style={styles.metric}>
                    <Text style={styles.metricLabel}>Last Contact</Text>
                    <Text style={styles.metricValue}>{client.lastContact}</Text>
                  </View>
                  <View style={styles.metric}>
                    <Text style={styles.metricLabel}>Response Time</Text>
                    <Text style={styles.metricValue}>{client.responseTime}</Text>
                  </View>
                  <View style={styles.metric}>
                    <Text style={styles.metricLabel}>Satisfaction</Text>
                    <Text style={styles.metricValue}>{client.satisfaction}/5</Text>
                  </View>
                </View>
              </View>
            ))}
          </View>
        </ScrollView>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000000",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    color: "#FFFFFF",
    fontSize: 16,
  },
  accessDenied: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 32,
    gap: 16,
  },
  accessDeniedTitle: {
    color: "#FFFFFF",
    fontSize: 20,
    fontWeight: "600",
  },
  accessDeniedText: {
    color: "#9CA3AF",
    fontSize: 14,
    textAlign: "center",
    lineHeight: 20,
  },
  header: {
    padding: 16,
    paddingTop: 80,
    gap: 16,
  },
  title: {
    color: "#FFFFFF",
    fontSize: 32,
    fontWeight: "300",
    letterSpacing: -1,
  },
  tabSelector: {
    flexDirection: "row",
    backgroundColor: "#18181B",
    borderRadius: 12,
    padding: 4,
  },
  tab: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: "center",
  },
  tabActive: {
    backgroundColor: "#0099FF",
  },
  tabText: {
    color: "#9CA3AF",
    fontSize: 14,
    fontWeight: "500",
  },
  tabTextActive: {
    color: "white",
  },
  kanbanContainer: {
    paddingHorizontal: 16,
    paddingBottom: 100,
    gap: 16,
  },
  kanbanColumn: {
    width: 280,
    backgroundColor: "#18181B",
    borderRadius: 16,
    padding: 16,
    height: "100%",
  },
  columnHeader: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
    marginBottom: 16,
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  columnTitle: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
    flex: 1,
  },
  columnCount: {
    color: "#9CA3AF",
    fontSize: 12,
    backgroundColor: "#374151",
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
  },
  columnScroll: {
    flex: 1,
  },
  issueCard: {
    backgroundColor: "#000000",
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    gap: 12,
  },
  issueHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    gap: 8,
  },
  issueTitle: {
    color: "#FFFFFF",
    fontSize: 14,
    fontWeight: "500",
    flex: 1,
  },
  priorityBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 6,
  },
  priorityText: {
    fontSize: 10,
    fontWeight: "600",
  },
  issueDescription: {
    color: "#D1D5DB",
    fontSize: 12,
    lineHeight: 16,
  },
  assigneeContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  assigneeAvatar: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: "#0099FF",
    justifyContent: "center",
    alignItems: "center",
  },
  assigneeText: {
    color: "white",
    fontSize: 10,
    fontWeight: "600",
  },
  assigneeName: {
    color: "#9CA3AF",
    fontSize: 12,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 100,
  },
  clientHealthSection: {
    gap: 16,
  },
  sectionTitle: {
    color: "#FFFFFF",
    fontSize: 20,
    fontWeight: "600",
    letterSpacing: -0.5,
  },
  clientCard: {
    backgroundColor: "#18181B",
    borderRadius: 16,
    padding: 20,
    gap: 16,
  },
  clientHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
  },
  clientInfo: {
    flex: 1,
    gap: 4,
  },
  clientName: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
  },
  clientProject: {
    color: "#9CA3AF",
    fontSize: 14,
  },
  healthBadge: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  healthText: {
    fontSize: 10,
    fontWeight: "600",
  },
  clientMetrics: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  metric: {
    alignItems: "center",
    gap: 4,
  },
  metricLabel: {
    color: "#9CA3AF",
    fontSize: 12,
  },
  metricValue: {
    color: "#FFFFFF",
    fontSize: 14,
    fontWeight: "500",
  },
});