import React from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";

const TIER_FEATURES = {
  free: {
    name: "Free",
    color: "#6B7280",
    features: ["Demo agents", "5 API calls/day"],
    price: "$0",
  },
  starter: {
    name: "Starter",
    color: "#0099FF",
    features: ["Email support", "100 calls/day"],
    price: "$29",
  },
  pro: {
    name: "Pro",
    color: "#9B59FF",
    features: ["Roam HQ private lobby", "1,000 calls/day", "Access Builder"],
    price: "$99",
  },
  enterprise: {
    name: "Enterprise",
    color: "#00C896",
    features: ["Dedicated Roam workspace", "On-call engineer", "Unlimited calls"],
    price: "Custom",
  },
};

const TOOLS = [
  {
    id: "agents",
    name: "AI Agents",
    description: "Deploy intelligent agents for your workflows",
    icon: "robot-outline",
    color: "#0099FF",
    minTier: "free",
  },
  {
    id: "builder",
    name: "Agent Builder",
    description: "Create custom AI agents with visual tools",
    icon: "construct-outline",
    color: "#9B59FF",
    minTier: "pro",
  },
  {
    id: "console",
    name: "Client Console",
    description: "Manage your account and view analytics",
    icon: "desktop-outline",
    color: "#00C896",
    minTier: "free",
  },
  {
    id: "analytics",
    name: "Analytics",
    description: "Deep insights into your AI usage",
    icon: "analytics-outline",
    color: "#FF914D",
    minTier: "starter",
  },
];

export default function Toolbox() {
  const currentUser = useQuery(api.auth.getCurrentUser);
  const invoices = useQuery(api.invoices.getUserInvoices);

  // Check if user is a client
  if (!currentUser || currentUser.role !== "client") {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.accessDenied}>
          <Ionicons name="construct-outline" size={48} color="#6B7280" />
          <Text style={styles.accessDeniedTitle}>Toolbox Access</Text>
          <Text style={styles.accessDeniedText}>
            The ALIAS Toolbox is exclusively for client accounts.
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  const userTier = currentUser.toolboxTier || "free";
  const tierConfig = TIER_FEATURES[userTier];
  
  const getTierIndex = (tier: string) => {
    return Object.keys(TIER_FEATURES).indexOf(tier);
  };

  const canAccessTool = (minTier: string) => {
    return getTierIndex(userTier) >= getTierIndex(minTier);
  };

  const handleToolPress = (tool: typeof TOOLS[0]) => {
    if (!canAccessTool(tool.minTier)) {
      Alert.alert(
        "Upgrade Required",
        `This feature requires ${TIER_FEATURES[tool.minTier as keyof typeof TIER_FEATURES].name} tier or higher.`,
        [
          { text: "Cancel", style: "cancel" },
          { text: "Upgrade", onPress: () => handleUpgrade() },
        ]
      );
      return;
    }

    if (tool.id === "console") {
      router.push("/toolbox/console");
    } else {
      Alert.alert("Coming Soon", `${tool.name} will be available soon!`);
    }
  };

  const handleUpgrade = () => {
    Alert.alert("Upgrade", "Contact our team to upgrade your subscription.");
  };

  // Check for overdue invoices or expiring subscription
  const hasOverdueInvoices = invoices?.some(inv => inv.status === "overdue");
  const isExpiringSoon = false; // TODO: Implement expiry check

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView 
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>ALIAS Toolbox</Text>
          <Text style={styles.subtitle}>
            Your AI-powered productivity suite
          </Text>
        </View>

        {/* Current Plan */}
        <View style={styles.planCard}>
          <View style={styles.planHeader}>
            <View style={styles.planInfo}>
              <Text style={styles.planName}>{tierConfig.name} Plan</Text>
              <Text style={styles.planPrice}>{tierConfig.price}/month</Text>
            </View>
            <View style={[styles.planBadge, { backgroundColor: tierConfig.color + "20" }]}>
              <Text style={[styles.planBadgeText, { color: tierConfig.color }]}>
                CURRENT
              </Text>
            </View>
          </View>
          
          <View style={styles.planFeatures}>
            {tierConfig.features.map((feature, index) => (
              <View key={index} style={styles.featureItem}>
                <Ionicons name="checkmark-circle" size={16} color={tierConfig.color} />
                <Text style={styles.featureText}>{feature}</Text>
              </View>
            ))}
          </View>

          {userTier !== "enterprise" && (
            <TouchableOpacity style={styles.upgradeButton} onPress={handleUpgrade}>
              <Text style={styles.upgradeButtonText}>Upgrade Plan</Text>
              <Ionicons name="arrow-forward" size={16} color="white" />
            </TouchableOpacity>
          )}
        </View>

        {/* Alerts */}
        {(hasOverdueInvoices || isExpiringSoon) && (
          <View style={styles.alertCard}>
            <Ionicons name="warning" size={20} color="#FF914D" />
            <View style={styles.alertContent}>
              <Text style={styles.alertTitle}>Action Required</Text>
              <Text style={styles.alertText}>
                {hasOverdueInvoices && "You have overdue invoices. "}
                {isExpiringSoon && "Your subscription expires in less than 7 days."}
              </Text>
            </View>
          </View>
        )}

        {/* Tools Grid */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Available Tools</Text>
          <View style={styles.toolsGrid}>
            {TOOLS.map((tool) => {
              const canAccess = canAccessTool(tool.minTier);
              
              return (
                <TouchableOpacity
                  key={tool.id}
                  style={[styles.toolCard, !canAccess && styles.toolCardLocked]}
                  onPress={() => handleToolPress(tool)}
                  activeOpacity={0.8}
                >
                  <View style={styles.toolHeader}>
                    <View style={[
                      styles.toolIcon,
                      { backgroundColor: tool.color + "20" }
                    ]}>
                      <Ionicons 
                        name={tool.icon as any} 
                        size={24} 
                        color={canAccess ? tool.color : "#6B7280"} 
                      />
                    </View>
                    {!canAccess && (
                      <View style={styles.lockIcon}>
                        <Ionicons name="lock-closed" size={12} color="#6B7280" />
                      </View>
                    )}
                  </View>
                  
                  <View style={styles.toolContent}>
                    <Text style={[
                      styles.toolName,
                      !canAccess && styles.toolNameLocked
                    ]}>
                      {tool.name}
                    </Text>
                    <Text style={[
                      styles.toolDescription,
                      !canAccess && styles.toolDescriptionLocked
                    ]}>
                      {tool.description}
                    </Text>
                    
                    {!canAccess && (
                      <View style={styles.requiresTier}>
                        <Text style={styles.requiresTierText}>
                          Requires {TIER_FEATURES[tool.minTier as keyof typeof TIER_FEATURES].name}
                        </Text>
                      </View>
                    )}
                  </View>
                </TouchableOpacity>
              );
            })}
          </View>
        </View>

        {/* Usage Stats */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Usage This Month</Text>
          <View style={styles.usageCard}>
            <View style={styles.usageItem}>
              <Text style={styles.usageLabel}>API Calls</Text>
              <Text style={styles.usageValue}>247 / 1,000</Text>
              <View style={styles.usageBar}>
                <View style={[styles.usageProgress, { width: "24.7%" }]} />
              </View>
            </View>
            
            <View style={styles.usageItem}>
              <Text style={styles.usageLabel}>Agents Deployed</Text>
              <Text style={styles.usageValue}>3</Text>
            </View>
            
            <View style={styles.usageItem}>
              <Text style={styles.usageLabel}>Support Tickets</Text>
              <Text style={styles.usageValue}>1 Open</Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000000",
  },
  accessDenied: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 32,
    gap: 16,
  },
  accessDeniedTitle: {
    color: "#FFFFFF",
    fontSize: 20,
    fontWeight: "600",
  },
  accessDeniedText: {
    color: "#9CA3AF",
    fontSize: 14,
    textAlign: "center",
    lineHeight: 20,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingTop: 80,
    paddingBottom: 120, // Account for floating nav bar
    gap: 24,
  },
  header: {
    gap: 4,
  },
  title: {
    color: "#FFFFFF",
    fontSize: 32,
    fontWeight: "300",
    letterSpacing: -1,
  },
  subtitle: {
    color: "#9CA3AF",
    fontSize: 14,
  },
  planCard: {
    backgroundColor: "#18181B",
    borderRadius: 16,
    padding: 20,
    gap: 16,
  },
  planHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
  },
  planInfo: {
    gap: 4,
  },
  planName: {
    color: "#FFFFFF",
    fontSize: 18,
    fontWeight: "600",
  },
  planPrice: {
    color: "#0099FF",
    fontSize: 16,
    fontWeight: "500",
  },
  planBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  planBadgeText: {
    fontSize: 10,
    fontWeight: "600",
  },
  planFeatures: {
    gap: 8,
  },
  featureItem: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  featureText: {
    color: "#D1D5DB",
    fontSize: 14,
  },
  upgradeButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: 8,
    backgroundColor: "#0099FF",
    paddingVertical: 12,
    borderRadius: 12,
  },
  upgradeButtonText: {
    color: "white",
    fontSize: 14,
    fontWeight: "500",
  },
  alertCard: {
    flexDirection: "row",
    alignItems: "flex-start",
    gap: 12,
    backgroundColor: "#FF914D20",
    borderColor: "#FF914D40",
    borderWidth: 1,
    borderRadius: 12,
    padding: 16,
  },
  alertContent: {
    flex: 1,
    gap: 4,
  },
  alertTitle: {
    color: "#FF914D",
    fontSize: 14,
    fontWeight: "600",
  },
  alertText: {
    color: "#D1D5DB",
    fontSize: 13,
    lineHeight: 18,
  },
  section: {
    gap: 16,
  },
  sectionTitle: {
    color: "#FFFFFF",
    fontSize: 18,
    fontWeight: "600",
    letterSpacing: -0.5,
  },
  toolsGrid: {
    gap: 16,
  },
  toolCard: {
    backgroundColor: "#18181B",
    borderRadius: 16,
    padding: 20,
    gap: 16,
  },
  toolCardLocked: {
    opacity: 0.6,
  },
  toolHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
  },
  toolIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: "center",
    alignItems: "center",
  },
  lockIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: "#374151",
    justifyContent: "center",
    alignItems: "center",
  },
  toolContent: {
    gap: 8,
  },
  toolName: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
  },
  toolNameLocked: {
    color: "#9CA3AF",
  },
  toolDescription: {
    color: "#D1D5DB",
    fontSize: 14,
    lineHeight: 20,
  },
  toolDescriptionLocked: {
    color: "#6B7280",
  },
  requiresTier: {
    marginTop: 4,
  },
  requiresTierText: {
    color: "#FF914D",
    fontSize: 12,
    fontWeight: "500",
  },
  usageCard: {
    backgroundColor: "#18181B",
    borderRadius: 16,
    padding: 20,
    gap: 16,
  },
  usageItem: {
    gap: 8,
  },
  usageLabel: {
    color: "#9CA3AF",
    fontSize: 12,
    textTransform: "uppercase",
    letterSpacing: 1,
  },
  usageValue: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
  },
  usageBar: {
    height: 4,
    backgroundColor: "#374151",
    borderRadius: 2,
    overflow: "hidden",
  },
  usageProgress: {
    height: "100%",
    backgroundColor: "#0099FF",
    borderRadius: 2,
  },
});