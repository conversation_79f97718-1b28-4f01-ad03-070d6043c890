import React from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import ComponentGallery from "@/components/ComponentGallery";

export default function Home() {
  const currentUser = useQuery(api.auth.getCurrentUser);
  const stats = useQuery(api.stats.listHome);
  const recentActivity = useQuery(api.activity.listRecent);

  if (!currentUser) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading...</Text>
        </View>
      </SafeAreaView>
    );
  }

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return "Good morning";
    if (hour < 17) return "Good afternoon";
    return "Good evening";
  };

  const shortcuts = [
    {
      title: "New Project",
      icon: "add-circle-outline",
      color: "#0099FF",
      onPress: () => router.push("/projects"),
    },
    {
      title: "Voice Assistant",
      icon: "mic-outline",
      color: "#00C896",
      onPress: () => router.push("/assistant"),
    },
    {
      title: "Learning Hub",
      icon: "school-outline",
      color: "#9B59FF",
      onPress: () => router.push("/learn"),
    },
    {
      title: "Operations",
      icon: "analytics-outline",
      color: "#FF914D",
      onPress: () => router.push("/ops"),
    },
  ];

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <View>
            <Text style={styles.greeting}>
              {getGreeting()}, {currentUser.name.split(" ")[0]}
            </Text>
            <Text style={styles.roleText}>
              {currentUser.role.charAt(0).toUpperCase() + currentUser.role.slice(1)} • ALIAS HQ
            </Text>
          </View>
          <View style={styles.avatarContainer}>
            <Text style={styles.avatarText}>
              {currentUser.name.split(" ").map(n => n[0]).join("")}
            </Text>
          </View>
        </View>

        {/* Quick Stats */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quick Stats</Text>
          <View style={styles.statsGrid}>
            {stats?.map((stat, index) => (
              <View key={index} style={styles.statCard}>
                <Text style={styles.statValue}>{stat.value}</Text>
                <Text style={styles.statLabel}>{stat.label}</Text>
              </View>
            )) || [
              { label: "Active Projects", value: "12" },
              { label: "Team Members", value: "24" },
              { label: "Clients", value: "40+" },
            ].map((stat, index) => (
              <View key={index} style={styles.statCard}>
                <Text style={styles.statValue}>{stat.value}</Text>
                <Text style={styles.statLabel}>{stat.label}</Text>
              </View>
            ))}
          </View>
        </View>

        {/* Pinned Shortcuts */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.shortcutsGrid}>
            {shortcuts.map((shortcut, index) => (
              <TouchableOpacity
                key={index}
                style={styles.shortcutCard}
                onPress={shortcut.onPress}
                activeOpacity={0.8}
              >
                <View style={[styles.shortcutIcon, { backgroundColor: shortcut.color + "20" }]}>
                  <Ionicons name={shortcut.icon as any} size={24} color={shortcut.color} />
                </View>
                <Text style={styles.shortcutTitle}>{shortcut.title}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Component Gallery Preview */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Component Gallery</Text>
            <TouchableOpacity
              onPress={() => router.push('/projects')}
              style={styles.seeAllButton}
            >
              <Text style={styles.seeAllText}>See All</Text>
              <Ionicons name="chevron-forward" size={16} color="#0099FF" />
            </TouchableOpacity>
          </View>
          <View style={styles.galleryPreview}>
            <ComponentGallery
              editable={false}
              showCollaboration={true}
            />
          </View>
        </View>

        {/* Recent Activity */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Recent Activity</Text>
          <View style={styles.activityList}>
            {recentActivity?.map((activity, index) => (
              <View key={index} style={styles.activityItem}>
                <View style={styles.activityIcon}>
                  <Ionicons name={activity.icon as any} size={16} color="#0099FF" />
                </View>
                <View style={styles.activityContent}>
                  <Text style={styles.activityText}>{activity.text}</Text>
                  <Text style={styles.activityTime}>{activity.ts}</Text>
                </View>
              </View>
            )) || [
              { text: "Project 'ALIAS HQ' status updated to In Progress", icon: "briefcase", ts: "2 hours ago" },
              { text: "New course 'AI Fundamentals' completed", icon: "school", ts: "4 hours ago" },
              { text: "Client meeting scheduled for tomorrow", icon: "calendar", ts: "6 hours ago" },
            ].map((activity, index) => (
              <View key={index} style={styles.activityItem}>
                <View style={styles.activityIcon}>
                  <Ionicons name={activity.icon as any} size={16} color="#0099FF" />
                </View>
                <View style={styles.activityContent}>
                  <Text style={styles.activityText}>{activity.text}</Text>
                  <Text style={styles.activityTime}>{activity.ts}</Text>
                </View>
              </View>
            ))}
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000000",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    color: "#FFFFFF",
    fontSize: 16,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingTop: 80, // Account for mic button
    paddingBottom: 100, // Account for smaller floating nav bar
    gap: 24,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  greeting: {
    color: "#FFFFFF",
    fontSize: 28,
    fontWeight: "300",
    letterSpacing: -0.5,
  },
  roleText: {
    color: "#9CA3AF",
    fontSize: 14,
    marginTop: 4,
  },
  avatarContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: "#0099FF",
    justifyContent: "center",
    alignItems: "center",
  },
  avatarText: {
    color: "white",
    fontSize: 16,
    fontWeight: "600",
  },
  section: {
    gap: 16,
  },
  sectionTitle: {
    color: "#FFFFFF",
    fontSize: 18,
    fontWeight: "600",
    letterSpacing: -0.5,
  },
  statsGrid: {
    flexDirection: "row",
    gap: 12,
  },
  statCard: {
    flex: 1,
    backgroundColor: "#18181B",
    padding: 16,
    borderRadius: 16,
    alignItems: "center",
    gap: 4,
  },
  statValue: {
    color: "#0099FF",
    fontSize: 20,
    fontWeight: "700",
  },
  statLabel: {
    color: "#9CA3AF",
    fontSize: 12,
    textAlign: "center",
  },
  shortcutsGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 12,
  },
  shortcutCard: {
    width: "48%",
    backgroundColor: "#18181B",
    padding: 16,
    borderRadius: 16,
    alignItems: "center",
    gap: 12,
  },
  shortcutIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: "center",
    alignItems: "center",
  },
  shortcutTitle: {
    color: "#FFFFFF",
    fontSize: 14,
    fontWeight: "500",
    textAlign: "center",
  },
  activityList: {
    gap: 12,
  },
  activityItem: {
    flexDirection: "row",
    alignItems: "flex-start",
    gap: 12,
    backgroundColor: "#18181B",
    padding: 16,
    borderRadius: 12,
  },
  activityIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: "#0099FF20",
    justifyContent: "center",
    alignItems: "center",
  },
  activityContent: {
    flex: 1,
    gap: 4,
  },
  activityText: {
    color: "#FFFFFF",
    fontSize: 14,
    lineHeight: 20,
  },
  activityTime: {
    color: "#9CA3AF",
    fontSize: 12,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  seeAllButton: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
  },
  seeAllText: {
    color: "#0099FF",
    fontSize: 14,
    fontWeight: "500",
  },
  galleryPreview: {
    height: 300,
    borderRadius: 16,
    overflow: "hidden",
  },
});