import { Tabs } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import { TouchableOpacity, View, StyleSheet } from "react-native";
import { useRouter } from "expo-router";
import * as Haptics from "expo-haptics";
import { Platform } from "react-native";
import { BlurView } from "expo-blur";

export default function TabLayout() {
  const router = useRouter();

  const handleMicPress = () => {
    if (Platform.OS !== "web") {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
    router.push("/assistant");
  };

  // Semantic color mapping for navigation icons
  const getIconColor = (routeName: string, isActive: boolean) => {
    const semanticColors = {
      index: "#0099FF",      // Home - Primary blue
      projects: "#00C896",   // Projects - Success green
      learn: "#9B59FF",      // Learn - Purple for education
      ops: "#FF914D",        // Operations - Orange for monitoring
      toolbox: "#FFD500",    // Toolbox - Yellow for tools
      profile: "#FF6B9D",    // Profile - Pink for personal
    };

    if (isActive) {
      return semanticColors[routeName as keyof typeof semanticColors] || "#0099FF";
    }
    return "#6B7280"; // Inactive gray
  };

  return (
    <>
      <Tabs
        screenOptions={({ route }) => ({
          headerShown: false,
          tabBarStyle: {
            position: "absolute",
            bottom: 20,
            left: 20,
            right: 20,
            height: 80,
            backgroundColor: "transparent",
            borderTopWidth: 0,
            elevation: 0,
            shadowOpacity: 0,
          },
          tabBarBackground: () => (
            <BlurView
              intensity={80}
              tint="dark"
              style={styles.glassTabBar}
            />
          ),
          tabBarActiveTintColor: getIconColor(route.name, true),
          tabBarInactiveTintColor: "#6B7280",
          tabBarLabelStyle: {
            fontSize: 10,
            fontWeight: "600",
            marginTop: 4,
            marginBottom: 8,
          },
          tabBarIconStyle: {
            marginTop: 8,
          },
        })}
      >
        <Tabs.Screen
          name="index"
          options={({ route }) => ({
            title: "Home",
            tabBarIcon: ({ focused, size }) => (
              <View style={[styles.iconContainer, focused && styles.iconContainerActive]}>
                <Ionicons
                  name={focused ? "home" : "home-outline"}
                  size={size}
                  color={getIconColor(route.name, focused)}
                />
              </View>
            ),
          })}
        />
        <Tabs.Screen
          name="projects"
          options={({ route }) => ({
            title: "Projects",
            tabBarIcon: ({ focused, size }) => (
              <View style={[styles.iconContainer, focused && styles.iconContainerActive]}>
                <Ionicons
                  name={focused ? "briefcase" : "briefcase-outline"}
                  size={size}
                  color={getIconColor(route.name, focused)}
                />
              </View>
            ),
          })}
        />
        <Tabs.Screen
          name="learn"
          options={({ route }) => ({
            title: "Learn",
            tabBarIcon: ({ focused, size }) => (
              <View style={[styles.iconContainer, focused && styles.iconContainerActive]}>
                <Ionicons
                  name={focused ? "school" : "school-outline"}
                  size={size}
                  color={getIconColor(route.name, focused)}
                />
              </View>
            ),
          })}
        />
        <Tabs.Screen
          name="ops"
          options={({ route }) => ({
            title: "Ops",
            tabBarIcon: ({ focused, size }) => (
              <View style={[styles.iconContainer, focused && styles.iconContainerActive]}>
                <Ionicons
                  name={focused ? "analytics" : "analytics-outline"}
                  size={size}
                  color={getIconColor(route.name, focused)}
                />
              </View>
            ),
          })}
        />
        <Tabs.Screen
          name="toolbox"
          options={({ route }) => ({
            title: "Toolbox",
            tabBarIcon: ({ focused, size }) => (
              <View style={[styles.iconContainer, focused && styles.iconContainerActive]}>
                <Ionicons
                  name={focused ? "construct" : "construct-outline"}
                  size={size}
                  color={getIconColor(route.name, focused)}
                />
              </View>
            ),
            tabBarBadge: undefined, // TODO: Add badge for overdue invoices
          })}
        />
        <Tabs.Screen
          name="profile"
          options={({ route }) => ({
            title: "Profile",
            tabBarIcon: ({ focused, size }) => (
              <View style={[styles.iconContainer, focused && styles.iconContainerActive]}>
                <Ionicons
                  name={focused ? "person" : "person-outline"}
                  size={size}
                  color={getIconColor(route.name, focused)}
                />
              </View>
            ),
          })}
        />
      </Tabs>

      {/* Floating Mic Button */}
      <View style={styles.micContainer}>
        <TouchableOpacity
          style={styles.micButton}
          onPress={handleMicPress}
          activeOpacity={0.8}
        >
          <Ionicons name="mic" size={24} color="white" />
        </TouchableOpacity>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  glassTabBar: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    borderRadius: 25,
    backgroundColor: "rgba(0, 0, 0, 0.3)",
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.1)",
    overflow: "hidden",
    ...Platform.select({
      ios: {
        shadowColor: "#000",
        shadowOffset: {
          width: 0,
          height: 10,
        },
        shadowOpacity: 0.25,
        shadowRadius: 20,
      },
      android: {
        elevation: 10,
      },
    }),
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
    transition: "all 0.2s ease",
  },
  iconContainerActive: {
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    transform: [{ scale: 1.1 }],
    ...Platform.select({
      ios: {
        shadowColor: "#000",
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.2,
        shadowRadius: 4,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  micContainer: {
    position: "absolute",
    top: 60,
    right: 16,
    zIndex: 1000,
  },
  micButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: "#0099FF",
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#0099FF",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
});