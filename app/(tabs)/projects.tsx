import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ImageBackground,
  Platform,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Ionicons } from "@expo/vector-icons";
import * as Haptics from "expo-haptics";
import { router } from "expo-router";

const STATUS_LABELS = {
  backlog: "Backlog",
  in_progress: "In Progress", 
  review: "Review",
  qa: "QA",
  done: "Done",
  prototype: "Prototype",
};

const STATUS_COLORS = {
  backlog: "#6B7280",
  in_progress: "#0099FF",
  review: "#FF914D",
  qa: "#9B59FF",
  done: "#00C896",
  prototype: "#FFD500",
};

export default function Projects() {
  const projects = useQuery(api.projects.list);
  const currentUser = useQuery(api.auth.getCurrentUser);
  const [selectedStatus, setSelectedStatus] = useState<string | null>(null);

  const handlePress = () => {
    if (Platform.OS !== "web") {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
  };

  const handleProjectPress = (slug: string) => {
    handlePress();
    router.push(`/project/${slug}`);
  };

  const handleStatusFilter = (status: string) => {
    handlePress();
    setSelectedStatus(selectedStatus === status ? null : status);
  };

  if (projects === undefined || !currentUser) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading...</Text>
        </View>
      </SafeAreaView>
    );
  }

  const filteredProjects = selectedStatus 
    ? projects.filter(p => p.status === selectedStatus)
    : projects;

  const canEdit = ["admin", "lead", "consultant"].includes(currentUser.role);

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView 
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Projects</Text>
          <Text style={styles.subtitle}>
            {filteredProjects.length} project{filteredProjects.length !== 1 ? 's' : ''}
          </Text>
        </View>

        {/* Status Filter Chips */}
        <ScrollView 
          horizontal 
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.filtersContainer}
        >
          {Object.entries(STATUS_LABELS).map(([key, label]) => (
            <TouchableOpacity
              key={key}
              style={[
                styles.filterChip,
                selectedStatus === key && styles.filterChipActive,
                { borderColor: STATUS_COLORS[key as keyof typeof STATUS_COLORS] }
              ]}
              onPress={() => handleStatusFilter(key)}
              activeOpacity={0.8}
            >
              <Text style={[
                styles.filterChipText,
                selectedStatus === key && styles.filterChipTextActive,
                { color: selectedStatus === key ? "white" : STATUS_COLORS[key as keyof typeof STATUS_COLORS] }
              ]}>
                {label}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>

        {/* Projects Grid */}
        <View style={styles.projectsGrid}>
          {filteredProjects.map((project) => (
            <TouchableOpacity 
              key={project._id}
              style={styles.projectCard}
              onPress={() => handleProjectPress(project.slug)}
              activeOpacity={0.95}
            >
              <ImageBackground
                source={{ uri: project.imageUrl }}
                style={styles.projectBackground}
                imageStyle={styles.projectBackgroundImage}
              >
                <View style={styles.projectOverlay}>
                  <View style={styles.projectHeader}>
                    <View style={[
                      styles.statusBadge, 
                      { backgroundColor: STATUS_COLORS[project.status] + "20" }
                    ]}>
                      <Text style={[
                        styles.statusText, 
                        { color: STATUS_COLORS[project.status] }
                      ]}>
                        {STATUS_LABELS[project.status]}
                      </Text>
                    </View>
                  </View>
                  
                  <View style={styles.projectContent}>
                    <Text style={styles.projectTitle}>{project.title}</Text>
                    <Text style={styles.projectDescription} numberOfLines={2}>
                      {project.description}
                    </Text>
                    
                    <View style={styles.projectTags}>
                      {project.tags.slice(0, 2).map((tag, index) => (
                        <View 
                          key={index} 
                          style={[
                            styles.projectTag,
                            { 
                              backgroundColor: project.accentColor + "15", 
                              borderColor: project.accentColor + "30" 
                            }
                          ]}
                        >
                          <Text style={[
                            styles.projectTagText, 
                            { color: project.accentColor }
                          ]}>
                            {tag}
                          </Text>
                        </View>
                      ))}
                    </View>
                  </View>
                </View>
              </ImageBackground>
            </TouchableOpacity>
          ))}
        </View>

        {/* Empty State */}
        {filteredProjects.length === 0 && (
          <View style={styles.emptyState}>
            <Ionicons name="briefcase-outline" size={48} color="#6B7280" />
            <Text style={styles.emptyTitle}>No projects found</Text>
            <Text style={styles.emptyDescription}>
              {selectedStatus 
                ? `No projects with status "${STATUS_LABELS[selectedStatus as keyof typeof STATUS_LABELS]}"`
                : "No projects available"
              }
            </Text>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000000",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    color: "#FFFFFF",
    fontSize: 16,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingTop: 80, // Account for mic button
    paddingBottom: 120, // Account for floating nav bar
    gap: 24,
  },
  header: {
    gap: 4,
  },
  title: {
    color: "#FFFFFF",
    fontSize: 32,
    fontWeight: "300",
    letterSpacing: -1,
  },
  subtitle: {
    color: "#9CA3AF",
    fontSize: 14,
  },
  filtersContainer: {
    flexDirection: "row",
    gap: 8,
    paddingHorizontal: 4,
  },
  filterChip: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    backgroundColor: "transparent",
  },
  filterChipActive: {
    backgroundColor: "#0099FF",
    borderColor: "#0099FF",
  },
  filterChipText: {
    fontSize: 12,
    fontWeight: "500",
    textTransform: "uppercase",
    letterSpacing: 0.5,
  },
  filterChipTextActive: {
    color: "white",
  },
  projectsGrid: {
    gap: 16,
  },
  projectCard: {
    height: 280,
    borderRadius: 20,
    overflow: "hidden",
  },
  projectBackground: {
    flex: 1,
  },
  projectBackgroundImage: {
    borderRadius: 20,
  },
  projectOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.4)",
    padding: 20,
    justifyContent: "space-between",
  },
  projectHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
  },
  statusBadge: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 10,
    fontWeight: "600",
    textTransform: "uppercase",
    letterSpacing: 1,
  },
  projectContent: {
    gap: 8,
  },
  projectTitle: {
    color: "white",
    fontSize: 24,
    fontWeight: "300",
    letterSpacing: -0.5,
  },
  projectDescription: {
    color: "rgba(255, 255, 255, 0.8)",
    fontSize: 13,
    lineHeight: 18,
  },
  projectTags: {
    flexDirection: "row",
    gap: 6,
    marginTop: 4,
  },
  projectTag: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    borderWidth: 1,
  },
  projectTagText: {
    fontSize: 9,
    fontWeight: "500",
    textTransform: "uppercase",
    letterSpacing: 0.5,
  },
  emptyState: {
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 48,
    gap: 16,
  },
  emptyTitle: {
    color: "#FFFFFF",
    fontSize: 18,
    fontWeight: "600",
  },
  emptyDescription: {
    color: "#9CA3AF",
    fontSize: 14,
    textAlign: "center",
    maxWidth: 280,
  },
});