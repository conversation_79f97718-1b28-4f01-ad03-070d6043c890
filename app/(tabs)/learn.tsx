import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useQuery, useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Ionicons } from "@expo/vector-icons";

const CATEGORIES = [
  { id: "all", name: "All Courses", icon: "library-outline" },
  { id: "ai", name: "AI & ML", icon: "brain-outline" },
  { id: "development", name: "Development", icon: "code-outline" },
  { id: "design", name: "Design", icon: "color-palette-outline" },
  { id: "business", name: "Business", icon: "briefcase-outline" },
];

export default function Learn() {
  const currentUser = useQuery(api.auth.getCurrentUser);
  const courses = useQuery(api.courses.list);
  const userProgress = useQuery(api.courses.getUserProgress);
  const markComplete = useMutation(api.courses.markComplete);
  
  const [selectedCategory, setSelectedCategory] = useState("all");

  // Check permissions
  if (currentUser?.role === "viewer") {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.accessDenied}>
          <Ionicons name="lock-closed-outline" size={48} color="#6B7280" />
          <Text style={styles.accessDeniedTitle}>Access Restricted</Text>
          <Text style={styles.accessDeniedText}>
            Learning hub is not available for viewer accounts.
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!currentUser || courses === undefined) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading...</Text>
        </View>
      </SafeAreaView>
    );
  }

  const filteredCourses = selectedCategory === "all" 
    ? courses 
    : courses.filter(course => course.category === selectedCategory);

  const handleMarkComplete = async (courseId: string) => {
    try {
      await markComplete({ courseId });
      Alert.alert("Success", "Course marked as complete!");
    } catch (error) {
      Alert.alert("Error", "Failed to mark course as complete");
    }
  };

  const getProgressForCourse = (courseId: string) => {
    return userProgress?.find(p => p.courseId === courseId);
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView 
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Learning Hub</Text>
          <Text style={styles.subtitle}>
            Expand your skills with ALIAS courses
          </Text>
        </View>

        {/* Categories */}
        <ScrollView 
          horizontal 
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.categoriesContainer}
        >
          {CATEGORIES.map((category) => (
            <TouchableOpacity
              key={category.id}
              style={[
                styles.categoryChip,
                selectedCategory === category.id && styles.categoryChipActive
              ]}
              onPress={() => setSelectedCategory(category.id)}
              activeOpacity={0.8}
            >
              <Ionicons 
                name={category.icon as any} 
                size={16} 
                color={selectedCategory === category.id ? "white" : "#0099FF"} 
              />
              <Text style={[
                styles.categoryChipText,
                selectedCategory === category.id && styles.categoryChipTextActive
              ]}>
                {category.name}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>

        {/* Courses List */}
        <View style={styles.coursesList}>
          {filteredCourses.map((course) => {
            const progress = getProgressForCourse(course._id);
            const isCompleted = progress?.completed || false;
            
            return (
              <View key={course._id} style={styles.courseCard}>
                <View style={styles.courseHeader}>
                  <View style={styles.courseInfo}>
                    <Text style={styles.courseTitle}>{course.title}</Text>
                    <Text style={styles.courseDescription} numberOfLines={2}>
                      {course.description}
                    </Text>
                    {course.duration && (
                      <View style={styles.courseMeta}>
                        <Ionicons name="time-outline" size={14} color="#9CA3AF" />
                        <Text style={styles.courseMetaText}>{course.duration}</Text>
                      </View>
                    )}
                  </View>
                  <View style={styles.courseActions}>
                    {isCompleted ? (
                      <View style={styles.completedBadge}>
                        <Ionicons name="checkmark-circle" size={20} color="#00C896" />
                        <Text style={styles.completedText}>Complete</Text>
                      </View>
                    ) : (
                      <TouchableOpacity
                        style={styles.markCompleteButton}
                        onPress={() => handleMarkComplete(course._id)}
                        activeOpacity={0.8}
                      >
                        <Text style={styles.markCompleteText}>Mark Done</Text>
                      </TouchableOpacity>
                    )}
                  </View>
                </View>
                
                {course.videoUrl && (
                  <TouchableOpacity style={styles.playButton} activeOpacity={0.8}>
                    <Ionicons name="play-circle" size={24} color="#0099FF" />
                    <Text style={styles.playButtonText}>Watch Video</Text>
                  </TouchableOpacity>
                )}
                
                {course.notes && (
                  <View style={styles.notesSection}>
                    <Text style={styles.notesTitle}>Course Notes</Text>
                    <Text style={styles.notesText} numberOfLines={3}>
                      {course.notes}
                    </Text>
                  </View>
                )}
              </View>
            );
          })}
        </View>

        {/* Empty State */}
        {filteredCourses.length === 0 && (
          <View style={styles.emptyState}>
            <Ionicons name="school-outline" size={48} color="#6B7280" />
            <Text style={styles.emptyTitle}>No courses found</Text>
            <Text style={styles.emptyDescription}>
              No courses available in this category yet.
            </Text>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000000",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    color: "#FFFFFF",
    fontSize: 16,
  },
  accessDenied: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 32,
    gap: 16,
  },
  accessDeniedTitle: {
    color: "#FFFFFF",
    fontSize: 20,
    fontWeight: "600",
  },
  accessDeniedText: {
    color: "#9CA3AF",
    fontSize: 14,
    textAlign: "center",
    lineHeight: 20,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingTop: 80,
    gap: 24,
  },
  header: {
    gap: 4,
  },
  title: {
    color: "#FFFFFF",
    fontSize: 32,
    fontWeight: "300",
    letterSpacing: -1,
  },
  subtitle: {
    color: "#9CA3AF",
    fontSize: 14,
  },
  categoriesContainer: {
    flexDirection: "row",
    gap: 8,
    paddingHorizontal: 4,
  },
  categoryChip: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: "#0099FF",
    backgroundColor: "transparent",
    gap: 6,
  },
  categoryChipActive: {
    backgroundColor: "#0099FF",
    borderColor: "#0099FF",
  },
  categoryChipText: {
    fontSize: 12,
    fontWeight: "500",
    color: "#0099FF",
  },
  categoryChipTextActive: {
    color: "white",
  },
  coursesList: {
    gap: 16,
  },
  courseCard: {
    backgroundColor: "#18181B",
    borderRadius: 16,
    padding: 20,
    gap: 16,
  },
  courseHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    gap: 16,
  },
  courseInfo: {
    flex: 1,
    gap: 8,
  },
  courseTitle: {
    color: "#FFFFFF",
    fontSize: 18,
    fontWeight: "600",
    letterSpacing: -0.5,
  },
  courseDescription: {
    color: "#D1D5DB",
    fontSize: 14,
    lineHeight: 20,
  },
  courseMeta: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
  },
  courseMetaText: {
    color: "#9CA3AF",
    fontSize: 12,
  },
  courseActions: {
    alignItems: "flex-end",
  },
  completedBadge: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: "#00C89620",
    borderRadius: 12,
  },
  completedText: {
    color: "#00C896",
    fontSize: 12,
    fontWeight: "500",
  },
  markCompleteButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: "#0099FF",
    borderRadius: 12,
  },
  markCompleteText: {
    color: "white",
    fontSize: 12,
    fontWeight: "500",
  },
  playButton: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
    paddingVertical: 8,
  },
  playButtonText: {
    color: "#0099FF",
    fontSize: 14,
    fontWeight: "500",
  },
  notesSection: {
    gap: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: "#374151",
  },
  notesTitle: {
    color: "#FFFFFF",
    fontSize: 14,
    fontWeight: "500",
  },
  notesText: {
    color: "#D1D5DB",
    fontSize: 13,
    lineHeight: 18,
  },
  emptyState: {
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 48,
    gap: 16,
  },
  emptyTitle: {
    color: "#FFFFFF",
    fontSize: 18,
    fontWeight: "600",
  },
  emptyDescription: {
    color: "#9CA3AF",
    fontSize: 14,
    textAlign: "center",
    maxWidth: 280,
  },
});