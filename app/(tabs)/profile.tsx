import React from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useQuery, useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";

const ROLE_COLORS = {
  admin: "#EF4444",
  lead: "#0099FF",
  consultant: "#9B59FF",
  client: "#00C896",
  viewer: "#6B7280",
};

const DEMO_ROLES = [
  { key: "admin", label: "Admin", description: "Full system access" },
  { key: "lead", label: "Lead", description: "Sales & account management" },
  { key: "consultant", label: "Consultant", description: "Delivery & R&D" },
  { key: "client", label: "Client", description: "Toolbox & console access" },
  { key: "viewer", label: "Viewer", description: "Read-only access" },
];

export default function Profile() {
  const currentUser = useQuery(api.auth.getCurrentUser);
  const switchRole = useMutation(api.auth.switchRole);

  if (!currentUser) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading...</Text>
        </View>
      </SafeAreaView>
    );
  }

  const handleRoleSwitch = async (newRole: string) => {
    try {
      await switchRole({ role: newRole as any });
      Alert.alert("Role Changed", `Switched to ${newRole} role. Restart the app to see changes.`);
    } catch (error) {
      Alert.alert("Error", "Failed to switch role");
    }
  };

  const handleSettingsPress = () => {
    if (currentUser.role === "admin") {
      router.push("/settings");
    } else {
      Alert.alert("Access Denied", "Settings are only available for admin users.");
    }
  };

  const profileSections = [
    {
      title: "Account",
      items: [
        {
          icon: "person-outline",
          label: "Personal Information",
          value: currentUser.name,
          onPress: () => Alert.alert("Coming Soon", "Profile editing will be available soon."),
        },
        {
          icon: "mail-outline",
          label: "Email",
          value: currentUser.email,
          onPress: () => {},
        },
        {
          icon: "shield-outline",
          label: "Role",
          value: currentUser.role.charAt(0).toUpperCase() + currentUser.role.slice(1),
          color: ROLE_COLORS[currentUser.role],
          onPress: () => {},
        },
      ],
    },
    {
      title: "Preferences",
      items: [
        {
          icon: "moon-outline",
          label: "Dark Mode",
          value: "Enabled",
          onPress: () => Alert.alert("Coming Soon", "Theme switching will be available soon."),
        },
        {
          icon: "notifications-outline",
          label: "Notifications",
          value: "All",
          onPress: () => Alert.alert("Coming Soon", "Notification settings will be available soon."),
        },
      ],
    },
    {
      title: "Demo Features",
      items: [
        {
          icon: "swap-horizontal-outline",
          label: "Switch Role",
          value: "Demo Mode",
          onPress: () => {
            Alert.alert(
              "Switch Role",
              "Choose a role to demo different app features:",
              DEMO_ROLES.map(role => ({
                text: `${role.label} - ${role.description}`,
                onPress: () => handleRoleSwitch(role.key),
              })).concat([{ text: "Cancel", style: "cancel" }])
            );
          },
        },
      ],
    },
    {
      title: "System",
      items: [
        {
          icon: "settings-outline",
          label: "Settings",
          value: currentUser.role === "admin" ? "Available" : "Admin Only",
          onPress: handleSettingsPress,
          disabled: currentUser.role !== "admin",
        },
        {
          icon: "help-circle-outline",
          label: "Help & Support",
          value: "",
          onPress: () => Alert.alert("Support", "Contact <EMAIL> for assistance."),
        },
        {
          icon: "information-circle-outline",
          label: "About",
          value: "v1.0.0",
          onPress: () => Alert.alert("ALIAS HQ", "Built with Expo & Convex\nVersion 1.0.0"),
        },
      ],
    },
  ];

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView 
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.avatarContainer}>
            <Text style={styles.avatarText}>
              {currentUser.name.split(" ").map(n => n[0]).join("")}
            </Text>
          </View>
          <Text style={styles.userName}>{currentUser.name}</Text>
          <View style={[styles.roleBadge, { backgroundColor: ROLE_COLORS[currentUser.role] + "20" }]}>
            <Text style={[styles.roleText, { color: ROLE_COLORS[currentUser.role] }]}>
              {currentUser.role.toUpperCase()}
            </Text>
          </View>
        </View>

        {/* Profile Sections */}
        {profileSections.map((section, sectionIndex) => (
          <View key={sectionIndex} style={styles.section}>
            <Text style={styles.sectionTitle}>{section.title}</Text>
            <View style={styles.sectionContent}>
              {section.items.map((item, itemIndex) => (
                <TouchableOpacity
                  key={itemIndex}
                  style={[styles.profileItem, item.disabled && styles.profileItemDisabled]}
                  onPress={item.onPress}
                  activeOpacity={0.8}
                  disabled={item.disabled}
                >
                  <View style={styles.profileItemLeft}>
                    <View style={[
                      styles.profileItemIcon,
                      { backgroundColor: (item.color || "#0099FF") + "20" }
                    ]}>
                      <Ionicons 
                        name={item.icon as any} 
                        size={20} 
                        color={item.disabled ? "#6B7280" : (item.color || "#0099FF")} 
                      />
                    </View>
                    <View style={styles.profileItemContent}>
                      <Text style={[
                        styles.profileItemLabel,
                        item.disabled && styles.profileItemLabelDisabled
                      ]}>
                        {item.label}
                      </Text>
                      {item.value && (
                        <Text style={[
                          styles.profileItemValue,
                          item.disabled && styles.profileItemValueDisabled
                        ]}>
                          {item.value}
                        </Text>
                      )}
                    </View>
                  </View>
                  {!item.disabled && (
                    <Ionicons name="chevron-forward" size={16} color="#6B7280" />
                  )}
                </TouchableOpacity>
              ))}
            </View>
          </View>
        ))}

        {/* Sign Out */}
        <TouchableOpacity 
          style={styles.signOutButton}
          onPress={() => Alert.alert("Sign Out", "Sign out functionality will be implemented with authentication.")}
          activeOpacity={0.8}
        >
          <Ionicons name="log-out-outline" size={20} color="#EF4444" />
          <Text style={styles.signOutText}>Sign Out</Text>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000000",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    color: "#FFFFFF",
    fontSize: 16,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingTop: 80,
    paddingBottom: 120, // Account for floating nav bar
    gap: 24,
  },
  header: {
    alignItems: "center",
    gap: 12,
    paddingVertical: 16,
  },
  avatarContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: "#0099FF",
    justifyContent: "center",
    alignItems: "center",
  },
  avatarText: {
    color: "white",
    fontSize: 24,
    fontWeight: "600",
  },
  userName: {
    color: "#FFFFFF",
    fontSize: 24,
    fontWeight: "600",
    letterSpacing: -0.5,
  },
  roleBadge: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  roleText: {
    fontSize: 10,
    fontWeight: "600",
    letterSpacing: 1,
  },
  section: {
    gap: 12,
  },
  sectionTitle: {
    color: "#9CA3AF",
    fontSize: 12,
    fontWeight: "600",
    textTransform: "uppercase",
    letterSpacing: 1,
    marginLeft: 4,
  },
  sectionContent: {
    backgroundColor: "#18181B",
    borderRadius: 16,
    overflow: "hidden",
  },
  profileItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#374151",
  },
  profileItemDisabled: {
    opacity: 0.5,
  },
  profileItemLeft: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
    flex: 1,
  },
  profileItemIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  profileItemContent: {
    flex: 1,
    gap: 2,
  },
  profileItemLabel: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "500",
  },
  profileItemLabelDisabled: {
    color: "#6B7280",
  },
  profileItemValue: {
    color: "#9CA3AF",
    fontSize: 14,
  },
  profileItemValueDisabled: {
    color: "#6B7280",
  },
  signOutButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: 8,
    backgroundColor: "#18181B",
    paddingVertical: 16,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: "#EF444420",
  },
  signOutText: {
    color: "#EF4444",
    fontSize: 16,
    fontWeight: "500",
  },
});