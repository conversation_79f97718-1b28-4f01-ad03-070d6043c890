import React, { useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ImageBackground,
  Image,
  Platform,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Ionicons } from "@expo/vector-icons";
import * as Haptics from "expo-haptics";
import { router } from "expo-router";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withDelay,
  withSpring,
  useDerivedValue,
} from "react-native-reanimated";

const STATUS_LABELS = {
  backlog: "Backlog",
  in_progress: "In Progress", 
  review: "Review",
  qa: "QA",
  done: "Done",
  prototype: "Prototype",
};

export default function Index() {
  const projects = useQuery(api.projects.list);
  
  // Animation values - Always declare these hooks at the top level
  const fadeAnim = useSharedValue(0);
  const slideAnim = useSharedValue(50);
  const scaleAnim = useSharedValue(0.9);
  
  // Animated number values
  const clientsValue = useSharedValue(0);
  const agentsValue = useSharedValue(0);
  const yearsValue = useSharedValue(0);

  // Derived values for animated numbers
  const clientsDisplay = useDerivedValue(() => {
    return Math.floor(clientsValue.value);
  });

  const agentsDisplay = useDerivedValue(() => {
    return Math.floor(agentsValue.value);
  });

  const yearsDisplay = useDerivedValue(() => {
    return Math.floor(yearsValue.value);
  });

  useEffect(() => {
    // Trigger animations when component mounts
    fadeAnim.value = withTiming(1, { duration: 800 });
    slideAnim.value = withTiming(0, { duration: 800 });
    scaleAnim.value = withSpring(1, { damping: 15, stiffness: 200 });
    
    // Animate numbers
    clientsValue.value = withDelay(1200, withTiming(40, { duration: 2000 }));
    agentsValue.value = withDelay(1400, withTiming(120, { duration: 2000 }));
    yearsValue.value = withDelay(1600, withTiming(3, { duration: 2000 }));
  }, []);

  const handlePress = () => {
    if (Platform.OS !== "web") {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
  };

  const handleProjectPress = (slug: string) => {
    handlePress();
    router.push(`/project/${slug}`);
  };

  // Animated styles
  const fadeInStyle = useAnimatedStyle(() => ({
    opacity: fadeAnim.value,
    transform: [
      { translateY: slideAnim.value },
      { scale: scaleAnim.value },
    ],
  }));

  const headerAnimatedStyle = useAnimatedStyle(() => ({
    opacity: withDelay(200, withTiming(fadeAnim.value, { duration: 600 })),
    transform: [
      { translateY: withDelay(200, withTiming(slideAnim.value, { duration: 600 })) },
    ],
  }));

  const titleAnimatedStyle = useAnimatedStyle(() => ({
    opacity: withDelay(400, withTiming(fadeAnim.value, { duration: 600 })),
    transform: [
      { translateY: withDelay(400, withTiming(slideAnim.value, { duration: 600 })) },
    ],
  }));

  const descriptionAnimatedStyle = useAnimatedStyle(() => ({
    opacity: withDelay(600, withTiming(fadeAnim.value, { duration: 600 })),
    transform: [
      { translateY: withDelay(600, withTiming(slideAnim.value, { duration: 600 })) },
    ],
  }));

  const statsAnimatedStyle = useAnimatedStyle(() => ({
    opacity: withDelay(800, withTiming(fadeAnim.value, { duration: 600 })),
    transform: [
      { translateY: withDelay(800, withTiming(slideAnim.value, { duration: 600 })) },
    ],
  }));

  const contactAnimatedStyle = useAnimatedStyle(() => ({
    opacity: withDelay(1000, withTiming(fadeAnim.value, { duration: 600 })),
    transform: [
      { translateY: withDelay(1000, withTiming(slideAnim.value, { duration: 600 })) },
    ],
  }));

  // Animated number styles
  const clientsAnimatedStyle = useAnimatedStyle(() => ({
    transform: [
      {
        scale: withSpring(clientsValue.value > 0 ? 1 : 0.8, {
          damping: 15,
          stiffness: 200,
        }),
      },
    ],
  }));

  const agentsAnimatedStyle = useAnimatedStyle(() => ({
    transform: [
      {
        scale: withSpring(agentsValue.value > 0 ? 1 : 0.8, {
          damping: 15,
          stiffness: 200,
        }),
      },
    ],
  }));

  const yearsAnimatedStyle = useAnimatedStyle(() => ({
    transform: [
      {
        scale: withSpring(yearsValue.value > 0 ? 1 : 0.8, {
          damping: 15,
          stiffness: 200,
        }),
      },
    ],
  }));

  // Show loading state while maintaining hook order
  if (projects === undefined) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading...</Text>
        </View>
      </SafeAreaView>
    );
  }

  const featuredProject = projects.find(p => p.featured);
  const otherProjects = projects.filter(p => !p.featured);

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView 
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Featured Project */}
        {featuredProject && (
          <TouchableOpacity 
            style={styles.featuredCard}
            onPress={() => handleProjectPress(featuredProject.slug)}
            activeOpacity={0.95}
          >
            <ImageBackground
              source={{ uri: featuredProject.imageUrl }}
              style={styles.featuredBackground}
              imageStyle={styles.featuredBackgroundImage}
            >
              <View style={styles.featuredOverlay}>
                <View style={styles.featuredHeader}>
                  <View style={styles.tagsContainer}>
                    {featuredProject.tags.slice(0, 2).map((tag, index) => (
                      <View key={index} style={styles.tag}>
                        <Text style={styles.tagText}>{tag}</Text>
                      </View>
                    ))}
                  </View>
                </View>
                
                <View style={styles.featuredContent}>
                  <Text style={styles.featuredTitle}>{featuredProject.title}</Text>
                  {featuredProject.quote && (
                    <View style={styles.quoteContainer}>
                      <Text style={styles.quote}>"{featuredProject.quote}"</Text>
                      {featuredProject.author && (
                        <View style={styles.authorContainer}>
                          <Image 
                            source={{ uri: featuredProject.author.avatar }}
                            style={styles.authorAvatar}
                          />
                          <View>
                            <Text style={styles.authorName}>{featuredProject.author.name}</Text>
                            <Text style={styles.authorRole}>{featuredProject.author.role}</Text>
                          </View>
                        </View>
                      )}
                    </View>
                  )}
                </View>
              </View>
            </ImageBackground>
          </TouchableOpacity>
        )}

        {/* All Other Projects */}
        <View style={styles.projectsGrid}>
          {otherProjects.map((project) => (
            <TouchableOpacity 
              key={project._id}
              style={styles.projectCard}
              onPress={() => handleProjectPress(project.slug)}
              activeOpacity={0.95}
            >
              <ImageBackground
                source={{ uri: project.imageUrl }}
                style={styles.projectBackground}
                imageStyle={styles.projectBackgroundImage}
              >
                <View style={styles.projectOverlay}>
                  <View style={styles.projectHeader}>
                    <View style={[styles.statusBadge, { backgroundColor: project.accentColor + "20" }]}>
                      <Text style={[styles.statusText, { color: project.accentColor }]}>
                        {STATUS_LABELS[project.status]}
                      </Text>
                    </View>
                  </View>
                  
                  <View style={styles.projectContent}>
                    <Text style={styles.projectTitle}>{project.title}</Text>
                    <Text style={styles.projectDescription} numberOfLines={2}>
                      {project.description}
                    </Text>
                    
                    <View style={styles.projectTags}>
                      {project.tags.slice(0, 2).map((tag, index) => (
                        <View 
                          key={index} 
                          style={[
                            styles.projectTag,
                            { backgroundColor: project.accentColor + "15", borderColor: project.accentColor + "30" }
                          ]}
                        >
                          <Text style={[styles.projectTagText, { color: project.accentColor }]}>{tag}</Text>
                        </View>
                      ))}
                    </View>
                  </View>
                </View>
              </ImageBackground>
            </TouchableOpacity>
          ))}
        </View>

        {/* Philosophy & Contact Card */}
        <Animated.View style={[styles.philosophyCard, fadeInStyle]}>
          <View style={styles.philosophyContent}>
            <Animated.View style={[styles.philosophyHeader, headerAnimatedStyle]}>
              <Ionicons name="bulb-outline" size={16} color="#9CA3AF" />
              <Text style={styles.philosophyHeaderText}>OUR PHILOSOPHY</Text>
            </Animated.View>
            
            <Animated.Text style={[styles.philosophyTitle, titleAnimatedStyle]}>
              Intelligence isn't magic — it's engineering.
            </Animated.Text>
            <Animated.Text style={[styles.philosophyDescription, descriptionAnimatedStyle]}>
              We fuse code, cognition, and radical candour to strip friction from every workflow.
              Every agent, every pixel, every deployment exists for one purpose: compounding human leverage.
            </Animated.Text>
            
            <Animated.View style={[styles.statsContainer, statsAnimatedStyle]}>
              <View style={styles.statItem}>
                <Animated.View style={clientsAnimatedStyle}>
                  <Animated.Text style={styles.statValue}>
                    {clientsDisplay.value}+
                  </Animated.Text>
                </Animated.View>
                <Text style={styles.statLabel}>CLIENTS</Text>
              </View>
              <View style={styles.statItem}>
                <Animated.View style={agentsAnimatedStyle}>
                  <Animated.Text style={styles.statValue}>
                    {agentsDisplay.value}+
                  </Animated.Text>
                </Animated.View>
                <Text style={styles.statLabel}>AI AGENTS ORCHESTRATED</Text>
              </View>
              <View style={styles.statItem}>
                <Animated.View style={yearsAnimatedStyle}>
                  <Animated.Text style={styles.statValue}>
                    {yearsDisplay.value} yr
                  </Animated.Text>
                </Animated.View>
                <Text style={styles.statLabel}>IN OPERATION</Text>
              </View>
            </Animated.View>
          </View>
          
          <Animated.View style={[styles.contactSection, contactAnimatedStyle]}>
            <TouchableOpacity style={styles.contactItem} onPress={handlePress}>
              <Ionicons name="mail-outline" size={16} color="#10B981" />
              <Text style={styles.contactText}><EMAIL></Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.contactItem} onPress={handlePress}>
              <Ionicons name="call-outline" size={16} color="#8B5CF6" />
              <Text style={styles.contactText}>+61 475 690 052</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.contactItem} onPress={handlePress}>
              <Ionicons name="globe-outline" size={16} color="#EC4899" />
              <Text style={styles.contactText}>alias.com.ai</Text>
            </TouchableOpacity>
          </Animated.View>
        </Animated.View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000000",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    color: "#FFFFFF",
    fontSize: 16,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    gap: 24,
  },
  
  // Featured Card
  featuredCard: {
    height: 400,
    borderRadius: 24,
    overflow: "hidden",
  },
  featuredBackground: {
    flex: 1,
  },
  featuredBackgroundImage: {
    borderRadius: 24,
  },
  featuredOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.3)",
    padding: 24,
    justifyContent: "space-between",
  },
  featuredHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
  },
  tagsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 8,
    flex: 1,
  },
  tag: {
    backgroundColor: "rgba(255, 255, 255, 0.15)",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.3)",
  },
  tagText: {
    color: "rgba(255, 255, 255, 0.9)",
    fontSize: 10,
    fontWeight: "500",
    textTransform: "uppercase",
    letterSpacing: 1,
  },
  featuredContent: {
    gap: 16,
  },
  featuredTitle: {
    color: "white",
    fontSize: 36,
    fontWeight: "300",
    letterSpacing: -1,
  },
  quoteContainer: {
    gap: 12,
  },
  quote: {
    color: "rgba(255, 255, 255, 0.85)",
    fontSize: 14,
    lineHeight: 20,
  },
  authorContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },
  authorAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
  },
  authorName: {
    color: "rgba(255, 255, 255, 0.9)",
    fontSize: 12,
    fontWeight: "500",
  },
  authorRole: {
    color: "rgba(255, 255, 255, 0.6)",
    fontSize: 12,
  },
  
  // Projects Grid
  projectsGrid: {
    gap: 16,
  },
  projectCard: {
    height: 280,
    borderRadius: 20,
    overflow: "hidden",
  },
  projectBackground: {
    flex: 1,
  },
  projectBackgroundImage: {
    borderRadius: 20,
  },
  projectOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.4)",
    padding: 20,
    justifyContent: "space-between",
  },
  projectHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
  },
  statusBadge: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 10,
    fontWeight: "600",
    textTransform: "uppercase",
    letterSpacing: 1,
  },
  projectContent: {
    gap: 8,
  },
  projectTitle: {
    color: "white",
    fontSize: 24,
    fontWeight: "300",
    letterSpacing: -0.5,
  },
  projectDescription: {
    color: "rgba(255, 255, 255, 0.8)",
    fontSize: 13,
    lineHeight: 18,
  },
  projectTags: {
    flexDirection: "row",
    gap: 6,
    marginTop: 4,
  },
  projectTag: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    borderWidth: 1,
  },
  projectTagText: {
    fontSize: 9,
    fontWeight: "500",
    textTransform: "uppercase",
    letterSpacing: 0.5,
  },
  
  // Philosophy Card
  philosophyCard: {
    backgroundColor: "#18181B",
    borderRadius: 24,
    overflow: "hidden",
    minHeight: 400,
  },
  philosophyContent: {
    padding: 32,
    flex: 1,
    gap: 16,
  },
  philosophyHeader: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  philosophyHeaderText: {
    color: "#9CA3AF",
    fontSize: 10,
    fontWeight: "400",
    textTransform: "uppercase",
    letterSpacing: 2,
  },
  philosophyTitle: {
    color: "white",
    fontSize: 24,
    fontWeight: "300",
    letterSpacing: -1,
  },
  philosophyDescription: {
    color: "#D1D5DB",
    fontSize: 14,
    lineHeight: 20,
  },
  statsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 16,
  },
  statItem: {
    alignItems: "center",
    flex: 1,
  },
  statValue: {
    fontSize: 20,
    fontWeight: "700",
    color: "#10B981",
  },
  statLabel: {
    color: "#9CA3AF",
    fontSize: 10,
    textTransform: "uppercase",
    letterSpacing: 1,
    marginTop: 4,
    textAlign: "center",
  },
  contactSection: {
    padding: 32,
    borderTopWidth: 1,
    borderTopColor: "#374151",
    gap: 12,
  },
  contactItem: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  contactText: {
    color: "white",
    fontSize: 14,
  },
});