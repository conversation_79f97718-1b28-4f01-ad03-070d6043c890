import "../polyfills"; // Import polyfills first
import { ConvexProvider, ConvexReactClient } from "convex/react";
import { Stack } from "expo-router";
import { useSplashScreen } from "@/hooks/useSplashScreen";
import CustomSplashScreen from "@/components/SplashScreen";
// Temporarily disable <PERSON> to test basic app functionality
// import { JazzProvider } from "@/lib/jazz/provider";

const convex = new ConvexReactClient(process.env.EXPO_PUBLIC_CONVEX_URL!, {
  unsavedChangesWarning: false,
});

export default function RootLayout() {
  const { isAppReady, showCustomSplash, onCustomSplashFinish, loadingProgress } = useSplashScreen();

  // Show custom splash screen while app is loading
  if (!isAppReady || showCustomSplash) {
    return (
      <CustomSplashScreen
        onAnimationFinish={onCustomSplashFinish}
        loadingProgress={loadingProgress}
      />
    );
  }

  return (
    <ConvexProvider client={convex}>
      {/* Temporarily disable Jazz to test basic app functionality */}
      {/* <JazzProvider> */}
        <Stack screenOptions={{ headerShown: false }} />
      {/* </JazzProvider> */}
    </ConvexProvider>
  );
}
